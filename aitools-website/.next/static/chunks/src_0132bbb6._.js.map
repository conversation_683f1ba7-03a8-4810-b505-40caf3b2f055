{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/src/components/LoadingSpinner.tsx"], "sourcesContent": ["'use client';\n\ninterface LoadingSpinnerProps {\n  size?: 'sm' | 'md' | 'lg';\n  className?: string;\n}\n\nexport default function LoadingSpinner({ size = 'md', className = '' }: LoadingSpinnerProps) {\n  const sizeClasses = {\n    sm: 'w-4 h-4',\n    md: 'w-8 h-8',\n    lg: 'w-12 h-12'\n  };\n\n  return (\n    <div className={`flex justify-center items-center ${className}`}>\n      <div className={`${sizeClasses[size]} border-4 border-gray-200 border-t-blue-600 rounded-full animate-spin`}></div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;;AAOe,SAAS,eAAe,EAAE,OAAO,IAAI,EAAE,YAAY,EAAE,EAAuB;IACzF,MAAM,cAAc;QAClB,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,qBACE,6LAAC;QAAI,WAAW,CAAC,iCAAiC,EAAE,WAAW;kBAC7D,cAAA,6LAAC;YAAI,WAAW,GAAG,WAAW,CAAC,KAAK,CAAC,qEAAqE,CAAC;;;;;;;;;;;AAGjH;KAZwB", "debugId": null}}, {"offset": {"line": 46, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/src/components/ErrorMessage.tsx"], "sourcesContent": ["'use client';\n\nimport { AlertCircle, X } from 'lucide-react';\n\ninterface ErrorMessageProps {\n  message: string;\n  onClose?: () => void;\n  className?: string;\n}\n\nexport default function ErrorMessage({ message, onClose, className = '' }: ErrorMessageProps) {\n  return (\n    <div className={`bg-red-50 border border-red-200 rounded-lg p-4 ${className}`}>\n      <div className=\"flex items-start\">\n        <AlertCircle className=\"w-5 h-5 text-red-500 mt-0.5 mr-3 flex-shrink-0\" />\n        <div className=\"flex-1\">\n          <p className=\"text-red-800 text-sm\">{message}</p>\n        </div>\n        {onClose && (\n          <button\n            onClick={onClose}\n            className=\"ml-3 text-red-400 hover:text-red-600 transition-colors\"\n          >\n            <X className=\"w-4 h-4\" />\n          </button>\n        )}\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAAA;AAFA;;;AAUe,SAAS,aAAa,EAAE,OAAO,EAAE,OAAO,EAAE,YAAY,EAAE,EAAqB;IAC1F,qBACE,6LAAC;QAAI,WAAW,CAAC,+CAA+C,EAAE,WAAW;kBAC3E,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC,uNAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;8BACvB,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAE,WAAU;kCAAwB;;;;;;;;;;;gBAEtC,yBACC,6LAAC;oBACC,SAAS;oBACT,WAAU;8BAEV,cAAA,6LAAC,+LAAA,CAAA,IAAC;wBAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;AAMzB;KAnBwB", "debugId": null}}, {"offset": {"line": 122, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/src/lib/env.ts"], "sourcesContent": ["/**\n * 动态环境配置工具\n * 根据运行时环境自动判断URL配置，而不是在配置文件中写死\n */\n\n/**\n * 获取当前运行环境的基础URL\n * 支持开发环境、生产环境和部署平台的自动检测\n */\nexport function getBaseUrl(): string {\n  // 1. 优先使用明确设置的环境变量\n  if (process.env.NEXT_PUBLIC_APP_URL) {\n    return process.env.NEXT_PUBLIC_APP_URL;\n  }\n\n  // 2. 在服务器端运行时\n  if (typeof window === 'undefined') {\n    // 构建时特殊处理：如果是构建阶段，使用固定的URL\n    if (process.env.NODE_ENV === 'production' && !process.env.VERCEL_URL && !process.env.NETLIFY) {\n      // 生产构建时，假设服务器将在3001端口运行\n      return 'http://localhost:3001';\n    }\n\n    // Vercel部署环境\n    if (process.env.VERCEL_URL) {\n      return `https://${process.env.VERCEL_URL}`;\n    }\n\n    // Netlify部署环境\n    if (process.env.NETLIFY && process.env.URL) {\n      return process.env.URL;\n    }\n\n    // Railway部署环境\n    if (process.env.RAILWAY_STATIC_URL) {\n      return process.env.RAILWAY_STATIC_URL;\n    }\n\n    // 其他云平台的通用环境变量\n    if (process.env.APP_URL) {\n      return process.env.APP_URL;\n    }\n\n    // 开发环境默认值\n    const port = process.env.PORT || '3001';\n    return `http://localhost:${port}`;\n  }\n\n  // 3. 在客户端运行时\n  if (typeof window !== 'undefined') {\n    const { protocol, hostname, port } = window.location;\n    return `${protocol}//${hostname}${port ? `:${port}` : ''}`;\n  }\n\n  // 4. 兜底默认值\n  return 'http://localhost:3001';\n}\n\n/**\n * 获取API基础URL\n */\nexport function getApiBaseUrl(): string {\n  // 1. 优先使用明确设置的环境变量\n  if (process.env.NEXT_PUBLIC_API_BASE_URL) {\n    return process.env.NEXT_PUBLIC_API_BASE_URL;\n  }\n\n  // 2. 基于基础URL构建API URL\n  const baseUrl = getBaseUrl();\n  return `${baseUrl}/api`;\n}\n\n/**\n * 获取NextAuth URL\n * NextAuth需要这个URL来处理回调和重定向\n */\nexport function getNextAuthUrl(): string {\n  // 1. 优先使用明确设置的环境变量\n  if (process.env.NEXTAUTH_URL) {\n    return process.env.NEXTAUTH_URL;\n  }\n\n  // 2. 基于基础URL构建\n  return getBaseUrl();\n}\n\n/**\n * 获取当前运行环境\n */\nexport function getEnvironment(): 'development' | 'production' | 'test' {\n  return (process.env.NODE_ENV as any) || 'development';\n}\n\n/**\n * 检查是否在开发环境\n */\nexport function isDevelopment(): boolean {\n  return getEnvironment() === 'development';\n}\n\n/**\n * 检查是否在生产环境\n */\nexport function isProduction(): boolean {\n  return getEnvironment() === 'production';\n}\n\n/**\n * 获取当前端口号\n */\nexport function getCurrentPort(): string {\n  // 服务器端\n  if (typeof window === 'undefined') {\n    return process.env.PORT || '3001';\n  }\n  \n  // 客户端\n  return window.location.port || (window.location.protocol === 'https:' ? '443' : '80');\n}\n\n/**\n * 动态环境配置对象\n * 可以在应用中直接使用这些配置\n */\nexport const dynamicEnv = {\n  baseUrl: getBaseUrl(),\n  apiBaseUrl: getApiBaseUrl(),\n  nextAuthUrl: getNextAuthUrl(),\n  environment: getEnvironment(),\n  isDevelopment: isDevelopment(),\n  isProduction: isProduction(),\n  port: getCurrentPort(),\n};\n\n/**\n * 在开发环境中打印配置信息（用于调试）\n */\nif (isDevelopment() && typeof window === 'undefined') {\n  console.log('🔧 Dynamic Environment Configuration:');\n  console.log('  Base URL:', dynamicEnv.baseUrl);\n  console.log('  API Base URL:', dynamicEnv.apiBaseUrl);\n  console.log('  NextAuth URL:', dynamicEnv.nextAuthUrl);\n  console.log('  Environment:', dynamicEnv.environment);\n  console.log('  Port:', dynamicEnv.port);\n}\n"], "names": [], "mappings": "AAAA;;;CAGC,GAED;;;CAGC;;;;;;;;;;AAGK;AAFC,SAAS;IACd,mBAAmB;IACnB,IAAI,gKAAA,CAAA,UAAO,CAAC,GAAG,CAAC,mBAAmB,EAAE;QACnC,OAAO,gKAAA,CAAA,UAAO,CAAC,GAAG,CAAC,mBAAmB;IACxC;IAEA,cAAc;IACd,uCAAmC;;IA8BnC;IAEA,aAAa;IACb,wCAAmC;QACjC,MAAM,EAAE,QAAQ,EAAE,QAAQ,EAAE,IAAI,EAAE,GAAG,OAAO,QAAQ;QACpD,OAAO,GAAG,SAAS,EAAE,EAAE,WAAW,OAAO,CAAC,CAAC,EAAE,MAAM,GAAG,IAAI;IAC5D;;AAIF;AAKO,SAAS;IACd,mBAAmB;IACnB,IAAI,gKAAA,CAAA,UAAO,CAAC,GAAG,CAAC,wBAAwB,EAAE;QACxC,OAAO,gKAAA,CAAA,UAAO,CAAC,GAAG,CAAC,wBAAwB;IAC7C;IAEA,sBAAsB;IACtB,MAAM,UAAU;IAChB,OAAO,GAAG,QAAQ,IAAI,CAAC;AACzB;AAMO,SAAS;IACd,mBAAmB;IACnB,IAAI,gKAAA,CAAA,UAAO,CAAC,GAAG,CAAC,YAAY,EAAE;QAC5B,OAAO,gKAAA,CAAA,UAAO,CAAC,GAAG,CAAC,YAAY;IACjC;IAEA,eAAe;IACf,OAAO;AACT;AAKO,SAAS;IACd,OAAO,mDAAiC;AAC1C;AAKO,SAAS;IACd,OAAO,qBAAqB;AAC9B;AAKO,SAAS;IACd,OAAO,qBAAqB;AAC9B;AAKO,SAAS;IACd,OAAO;IACP,uCAAmC;;IAEnC;IAEA,MAAM;IACN,OAAO,OAAO,QAAQ,CAAC,IAAI,IAAI,CAAC,OAAO,QAAQ,CAAC,QAAQ,KAAK,WAAW,QAAQ,IAAI;AACtF;AAMO,MAAM,aAAa;IACxB,SAAS;IACT,YAAY;IACZ,aAAa;IACb,aAAa;IACb,eAAe;IACf,cAAc;IACd,MAAM;AACR;AAEA;;CAEC,GACD,IAAI,mBAAmB,aAAkB,aAAa;;AAOtD", "debugId": null}}, {"offset": {"line": 212, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/src/lib/api.ts"], "sourcesContent": ["// API客户端工具类\nimport { getApiBaseUrl } from './env';\n\nconst API_BASE_URL = getApiBaseUrl();\n\nexport interface ApiResponse<T = any> {\n  success: boolean;\n  data?: T;\n  error?: string;\n  message?: string;\n  details?: string[];\n}\n\nexport interface PaginationInfo {\n  currentPage: number;\n  totalPages: number;\n  totalItems: number;\n  itemsPerPage: number;\n  hasNextPage: boolean;\n  hasPrevPage: boolean;\n}\n\nexport interface Tool {\n  _id: string;\n  name: string;\n  tagline?: string;\n  description: string;\n  longDescription?: string;\n  website: string;\n  logo?: string;\n  category: string;\n  tags: string[];\n  pricing: 'free' | 'freemium' | 'paid';\n  pricingDetails?: string;\n  screenshots?: string[];\n  submittedBy: string;\n  submittedAt: string;\n  launchDate?: string; // 实际发布日期，一般等于selectedLaunchDate\n  status: 'draft' | 'pending' | 'approved' | 'rejected'; // 去掉published状态\n  reviewNotes?: string;\n  reviewedBy?: string;\n  reviewedAt?: string;\n\n  // 发布日期选择相关\n  launchDateSelected?: boolean;\n  selectedLaunchDate?: string;\n  launchOption?: 'free' | 'paid';\n\n  // 付费相关\n  paymentRequired?: boolean;\n  paymentAmount?: number;\n  paymentStatus?: 'pending' | 'completed' | 'failed' | 'refunded';\n  orderId?: string;\n  paymentMethod?: string;\n  paidAt?: string;\n\n  views: number;\n  likes: number;\n  isActive: boolean;\n  createdAt: string;\n  updatedAt: string;\n}\n\nexport interface ToolsResponse {\n  tools: Tool[];\n  pagination: PaginationInfo;\n  stats?: {\n    total: number;\n    pending: number;\n    approved: number;\n    rejected: number;\n  };\n}\n\nexport interface AdminStats {\n  overview: {\n    totalTools: number;\n    pendingTools: number;\n    approvedTools: number;\n    rejectedTools: number;\n    totalViews: number;\n    totalLikes: number;\n    recentSubmissions: number;\n    recentApprovals: number;\n    recentRejections: number;\n    avgReviewTime: number;\n  };\n  categoryStats: Array<{\n    _id: string;\n    count: number;\n    totalViews: number;\n    totalLikes: number;\n  }>;\n  topTools: Array<{\n    _id: string;\n    name: string;\n    category: string;\n    views: number;\n    likes: number;\n  }>;\n  recentActivity: Array<{\n    _id: string;\n    name: string;\n    submittedBy: string;\n    submittedAt: string;\n    status: string;\n    reviewedAt?: string;\n    reviewedBy?: string;\n  }>;\n  dailyStats: Array<{\n    date: string;\n    day: string;\n    submissions: number;\n    approvals: number;\n    rejections: number;\n  }>;\n  timeRange: string;\n}\n\nexport interface Category {\n  id: string;\n  name: string;\n  count: number;\n  totalViews: number;\n  totalLikes: number;\n}\n\nexport interface CategoriesResponse {\n  categories: Category[];\n  overview: {\n    totalTools: number;\n    totalViews: number;\n    totalLikes: number;\n  };\n}\n\nclass ApiClient {\n  private baseURL: string;\n\n  constructor(baseURL: string = API_BASE_URL) {\n    this.baseURL = baseURL;\n  }\n\n  private async request<T>(\n    endpoint: string,\n    options: RequestInit = {}\n  ): Promise<ApiResponse<T>> {\n    try {\n      const url = `${this.baseURL}${endpoint}`;\n      const config: RequestInit = {\n        headers: {\n          'Content-Type': 'application/json',\n          ...options.headers,\n        },\n        ...options,\n      };\n\n      const response = await fetch(url, config);\n      const data = await response.json();\n\n      if (!response.ok) {\n        throw new Error(data.error || `HTTP error! status: ${response.status}`);\n      }\n\n      return data;\n    } catch (error) {\n      console.error('API request failed:', error);\n      return {\n        success: false,\n        error: error instanceof Error ? error.message : '请求失败',\n      };\n    }\n  }\n\n  // 工具相关API\n  async getTools(params?: {\n    page?: number;\n    limit?: number;\n    category?: string;\n    status?: string;\n    search?: string;\n    sort?: string;\n    order?: string;\n    dateFrom?: string;\n    dateTo?: string;\n  }): Promise<ApiResponse<ToolsResponse>> {\n    const searchParams = new URLSearchParams();\n    if (params) {\n      Object.entries(params).forEach(([key, value]) => {\n        if (value !== undefined) {\n          searchParams.append(key, value.toString());\n        }\n      });\n    }\n    const query = searchParams.toString();\n    return this.request<ToolsResponse>(`/tools${query ? `?${query}` : ''}`);\n  }\n\n  async getTool(id: string): Promise<ApiResponse<Tool>> {\n    return this.request<Tool>(`/tools/${id}`);\n  }\n\n  async createTool(toolData: Partial<Tool>): Promise<ApiResponse<Tool>> {\n    return this.request<Tool>('/tools', {\n      method: 'POST',\n      body: JSON.stringify(toolData),\n    });\n  }\n\n  async updateTool(id: string, toolData: Partial<Tool>): Promise<ApiResponse<Tool>> {\n    return this.request<Tool>(`/tools/${id}`, {\n      method: 'PUT',\n      body: JSON.stringify(toolData),\n    });\n  }\n\n  async deleteTool(id: string): Promise<ApiResponse<void>> {\n    return this.request<void>(`/tools/${id}`, {\n      method: 'DELETE',\n    });\n  }\n\n  async getLikedTools(params?: {\n    page?: number;\n    limit?: number;\n  }): Promise<ApiResponse<ToolsResponse>> {\n    const searchParams = new URLSearchParams();\n    if (params) {\n      Object.entries(params).forEach(([key, value]) => {\n        if (value !== undefined) {\n          searchParams.append(key, value.toString());\n        }\n      });\n    }\n    const query = searchParams.toString();\n    return this.request<ToolsResponse>(`/user/liked-tools${query ? `?${query}` : ''}`);\n  }\n\n  // 管理员API\n  async getAdminTools(params?: {\n    page?: number;\n    limit?: number;\n    status?: string;\n    search?: string;\n    sort?: string;\n    order?: string;\n  }): Promise<ApiResponse<ToolsResponse>> {\n    const searchParams = new URLSearchParams();\n    if (params) {\n      Object.entries(params).forEach(([key, value]) => {\n        if (value !== undefined) {\n          searchParams.append(key, value.toString());\n        }\n      });\n    }\n    const query = searchParams.toString();\n    return this.request<ToolsResponse>(`/admin/tools${query ? `?${query}` : ''}`);\n  }\n\n  async approveTool(id: string, data: {\n    reviewedBy?: string;\n    reviewNotes?: string;\n    launchDate?: string;\n  }): Promise<ApiResponse<Tool>> {\n    return this.request<Tool>(`/admin/tools/${id}/approve`, {\n      method: 'POST',\n      body: JSON.stringify(data),\n    });\n  }\n\n  async rejectTool(id: string, data: {\n    reviewedBy?: string;\n    rejectReason: string;\n  }): Promise<ApiResponse<Tool>> {\n    return this.request<Tool>(`/admin/tools/${id}/reject`, {\n      method: 'POST',\n      body: JSON.stringify(data),\n    });\n  }\n\n  async getAdminStats(timeRange?: string): Promise<ApiResponse<AdminStats>> {\n    const query = timeRange ? `?timeRange=${timeRange}` : '';\n    return this.request<AdminStats>(`/admin/stats${query}`);\n  }\n\n  // 分类API\n  async getCategories(): Promise<ApiResponse<CategoriesResponse>> {\n    return this.request<CategoriesResponse>('/categories');\n  }\n\n  // 订单API\n  async getOrder(id: string): Promise<ApiResponse<any>> {\n    return this.request<any>(`/orders/${id}`);\n  }\n\n  async processOrderPayment(id: string, data: {\n    paymentMethod?: string;\n  }): Promise<ApiResponse<any>> {\n    return this.request<any>(`/orders/${id}/pay`, {\n      method: 'POST',\n      body: JSON.stringify(data),\n    });\n  }\n\n  // 用户API\n  async getCurrentUser(): Promise<ApiResponse<any>> {\n    return this.request<any>('/auth/me');\n  }\n\n  async updateProfile(data: {\n    name?: string;\n    avatar?: string;\n    bio?: string;\n    website?: string;\n    location?: string;\n  }): Promise<ApiResponse<any>> {\n    return this.request<any>('/auth/me', {\n      method: 'PUT',\n      body: JSON.stringify(data),\n    });\n  }\n}\n\n// 创建默认的API客户端实例\nexport const apiClient = new ApiClient();\n\n// 导出API客户端类以便在需要时创建新实例\nexport { ApiClient };\n"], "names": [], "mappings": "AAAA,YAAY;;;;;AACZ;;AAEA,MAAM,eAAe,CAAA,GAAA,oHAAA,CAAA,gBAAa,AAAD;AAqIjC,MAAM;IACI,QAAgB;IAExB,YAAY,UAAkB,YAAY,CAAE;QAC1C,IAAI,CAAC,OAAO,GAAG;IACjB;IAEA,MAAc,QACZ,QAAgB,EAChB,UAAuB,CAAC,CAAC,EACA;QACzB,IAAI;YACF,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,GAAG,UAAU;YACxC,MAAM,SAAsB;gBAC1B,SAAS;oBACP,gBAAgB;oBAChB,GAAG,QAAQ,OAAO;gBACpB;gBACA,GAAG,OAAO;YACZ;YAEA,MAAM,WAAW,MAAM,MAAM,KAAK;YAClC,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM,KAAK,KAAK,IAAI,CAAC,oBAAoB,EAAE,SAAS,MAAM,EAAE;YACxE;YAEA,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,uBAAuB;YACrC,OAAO;gBACL,SAAS;gBACT,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG;YAClD;QACF;IACF;IAEA,UAAU;IACV,MAAM,SAAS,MAUd,EAAuC;QACtC,MAAM,eAAe,IAAI;QACzB,IAAI,QAAQ;YACV,OAAO,OAAO,CAAC,QAAQ,OAAO,CAAC,CAAC,CAAC,KAAK,MAAM;gBAC1C,IAAI,UAAU,WAAW;oBACvB,aAAa,MAAM,CAAC,KAAK,MAAM,QAAQ;gBACzC;YACF;QACF;QACA,MAAM,QAAQ,aAAa,QAAQ;QACnC,OAAO,IAAI,CAAC,OAAO,CAAgB,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC,EAAE,OAAO,GAAG,IAAI;IACxE;IAEA,MAAM,QAAQ,EAAU,EAA8B;QACpD,OAAO,IAAI,CAAC,OAAO,CAAO,CAAC,OAAO,EAAE,IAAI;IAC1C;IAEA,MAAM,WAAW,QAAuB,EAA8B;QACpE,OAAO,IAAI,CAAC,OAAO,CAAO,UAAU;YAClC,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACvB;IACF;IAEA,MAAM,WAAW,EAAU,EAAE,QAAuB,EAA8B;QAChF,OAAO,IAAI,CAAC,OAAO,CAAO,CAAC,OAAO,EAAE,IAAI,EAAE;YACxC,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACvB;IACF;IAEA,MAAM,WAAW,EAAU,EAA8B;QACvD,OAAO,IAAI,CAAC,OAAO,CAAO,CAAC,OAAO,EAAE,IAAI,EAAE;YACxC,QAAQ;QACV;IACF;IAEA,MAAM,cAAc,MAGnB,EAAuC;QACtC,MAAM,eAAe,IAAI;QACzB,IAAI,QAAQ;YACV,OAAO,OAAO,CAAC,QAAQ,OAAO,CAAC,CAAC,CAAC,KAAK,MAAM;gBAC1C,IAAI,UAAU,WAAW;oBACvB,aAAa,MAAM,CAAC,KAAK,MAAM,QAAQ;gBACzC;YACF;QACF;QACA,MAAM,QAAQ,aAAa,QAAQ;QACnC,OAAO,IAAI,CAAC,OAAO,CAAgB,CAAC,iBAAiB,EAAE,QAAQ,CAAC,CAAC,EAAE,OAAO,GAAG,IAAI;IACnF;IAEA,SAAS;IACT,MAAM,cAAc,MAOnB,EAAuC;QACtC,MAAM,eAAe,IAAI;QACzB,IAAI,QAAQ;YACV,OAAO,OAAO,CAAC,QAAQ,OAAO,CAAC,CAAC,CAAC,KAAK,MAAM;gBAC1C,IAAI,UAAU,WAAW;oBACvB,aAAa,MAAM,CAAC,KAAK,MAAM,QAAQ;gBACzC;YACF;QACF;QACA,MAAM,QAAQ,aAAa,QAAQ;QACnC,OAAO,IAAI,CAAC,OAAO,CAAgB,CAAC,YAAY,EAAE,QAAQ,CAAC,CAAC,EAAE,OAAO,GAAG,IAAI;IAC9E;IAEA,MAAM,YAAY,EAAU,EAAE,IAI7B,EAA8B;QAC7B,OAAO,IAAI,CAAC,OAAO,CAAO,CAAC,aAAa,EAAE,GAAG,QAAQ,CAAC,EAAE;YACtD,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACvB;IACF;IAEA,MAAM,WAAW,EAAU,EAAE,IAG5B,EAA8B;QAC7B,OAAO,IAAI,CAAC,OAAO,CAAO,CAAC,aAAa,EAAE,GAAG,OAAO,CAAC,EAAE;YACrD,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACvB;IACF;IAEA,MAAM,cAAc,SAAkB,EAAoC;QACxE,MAAM,QAAQ,YAAY,CAAC,WAAW,EAAE,WAAW,GAAG;QACtD,OAAO,IAAI,CAAC,OAAO,CAAa,CAAC,YAAY,EAAE,OAAO;IACxD;IAEA,QAAQ;IACR,MAAM,gBAA0D;QAC9D,OAAO,IAAI,CAAC,OAAO,CAAqB;IAC1C;IAEA,QAAQ;IACR,MAAM,SAAS,EAAU,EAA6B;QACpD,OAAO,IAAI,CAAC,OAAO,CAAM,CAAC,QAAQ,EAAE,IAAI;IAC1C;IAEA,MAAM,oBAAoB,EAAU,EAAE,IAErC,EAA6B;QAC5B,OAAO,IAAI,CAAC,OAAO,CAAM,CAAC,QAAQ,EAAE,GAAG,IAAI,CAAC,EAAE;YAC5C,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACvB;IACF;IAEA,QAAQ;IACR,MAAM,iBAA4C;QAChD,OAAO,IAAI,CAAC,OAAO,CAAM;IAC3B;IAEA,MAAM,cAAc,IAMnB,EAA6B;QAC5B,OAAO,IAAI,CAAC,OAAO,CAAM,YAAY;YACnC,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACvB;IACF;AACF;AAGO,MAAM,YAAY,IAAI", "debugId": null}}, {"offset": {"line": 359, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/src/app/%5Blocale%5D/settings/page.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState, useEffect } from 'react';\nimport { useSession } from 'next-auth/react';\nimport { useRouter } from '@/i18n/routing';\nimport { Link } from '@/i18n/routing';\nimport LoadingSpinner from '@/components/LoadingSpinner';\nimport ErrorMessage from '@/components/ErrorMessage';\nimport { apiClient } from '@/lib/api';\nimport {\n  User,\n  Bell,\n  Shield,\n  Save,\n  ArrowLeft,\n  Camera,\n  Trash2\n} from 'lucide-react';\n\nexport default function SettingsPage() {\n  const { data: session, status, update } = useSession();\n  const router = useRouter();\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState('');\n  const [success, setSuccess] = useState('');\n  \n  // 用户信息表单\n  const [profileForm, setProfileForm] = useState({\n    name: '',\n    email: '',\n    bio: '',\n    website: '',\n    location: ''\n  });\n\n  // 通知设置\n  const [notificationSettings, setNotificationSettings] = useState({\n    emailNotifications: true,\n    toolApprovalNotifications: true,\n    weeklyDigest: false,\n    marketingEmails: false\n  });\n\n  // 隐私设置\n  const [privacySettings, setPrivacySettings] = useState({\n    profileVisibility: 'public',\n    showEmail: false,\n    showSubmittedTools: true\n  });\n\n  useEffect(() => {\n    if (status === 'unauthenticated') {\n      router.push('/');\n      return;\n    }\n\n    if (status === 'authenticated' && session?.user) {\n      // 从服务器加载完整的用户信息\n      loadUserProfile();\n    }\n  }, [status, session, router]);\n\n  const loadUserProfile = async () => {\n    try {\n      const response = await apiClient.getCurrentUser();\n      if (response.success && response.data) {\n        setProfileForm({\n          name: response.data.name || '',\n          email: response.data.email || '',\n          bio: response.data.bio || '',\n          website: response.data.website || '',\n          location: response.data.location || ''\n        });\n      } else {\n        // 如果API调用失败，使用session中的基本信息\n        setProfileForm({\n          name: session?.user?.name || '',\n          email: session?.user?.email || '',\n          bio: '',\n          website: '',\n          location: ''\n        });\n      }\n    } catch (error) {\n      console.error('Failed to load user profile:', error);\n      // 如果API调用失败，使用session中的基本信息\n      setProfileForm({\n        name: session?.user?.name || '',\n        email: session?.user?.email || '',\n        bio: '',\n        website: '',\n        location: ''\n      });\n    }\n  };\n\n  const handleProfileSubmit = async (e: React.FormEvent) => {\n    e.preventDefault();\n    setLoading(true);\n    setError('');\n    setSuccess('');\n\n    try {\n      const response = await apiClient.updateProfile({\n        name: profileForm.name,\n        bio: profileForm.bio,\n        website: profileForm.website,\n        location: profileForm.location\n      });\n\n      if (response.success) {\n        // 更新session\n        await update({\n          ...session,\n          user: {\n            ...session?.user,\n            name: profileForm.name\n          }\n        });\n\n        setSuccess('个人资料已更新');\n      } else {\n        setError(response.error || '更新失败，请重试');\n      }\n    } catch (err) {\n      console.error('Profile update error:', err);\n      setError('更新失败，请重试');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleNotificationSubmit = async (e: React.FormEvent) => {\n    e.preventDefault();\n    setLoading(true);\n    setError('');\n    setSuccess('');\n\n    try {\n      // TODO: 实现更新通知设置API\n      await new Promise(resolve => setTimeout(resolve, 1000));\n      setSuccess('通知设置已更新');\n    } catch (err) {\n      setError('更新失败，请重试');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handlePrivacySubmit = async (e: React.FormEvent) => {\n    e.preventDefault();\n    setLoading(true);\n    setError('');\n    setSuccess('');\n\n    try {\n      // TODO: 实现更新隐私设置API\n      await new Promise(resolve => setTimeout(resolve, 1000));\n      setSuccess('隐私设置已更新');\n    } catch (err) {\n      setError('更新失败，请重试');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  if (status === 'loading') {\n    return (\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n        <LoadingSpinner size=\"lg\" className=\"py-20\" />\n      </div>\n    );\n  }\n\n  if (!session) {\n    return null;\n  }\n\n  return (\n    <div className=\"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n        {/* Header */}\n        <div className=\"flex items-center mb-8\">\n          <Link\n            href=\"/profile\"\n            className=\"mr-4 p-2 text-gray-400 hover:text-gray-600 transition-colors\"\n          >\n            <ArrowLeft className=\"h-5 w-5\" />\n          </Link>\n          <div>\n            <h1 className=\"text-3xl font-bold text-gray-900\">账户设置</h1>\n            <p className=\"text-lg text-gray-600\">管理您的个人资料和偏好设置</p>\n          </div>\n        </div>\n\n        {/* Success/Error Messages */}\n        {success && (\n          <div className=\"mb-6 p-4 bg-green-50 border border-green-200 rounded-lg\">\n            <p className=\"text-green-800\">{success}</p>\n          </div>\n        )}\n        {error && (\n          <ErrorMessage\n            message={error}\n            onClose={() => setError('')}\n            className=\"mb-6\"\n          />\n        )}\n\n        <div className=\"space-y-8\">\n          {/* 个人资料 */}\n          <div className=\"bg-white rounded-lg shadow-sm border border-gray-200 p-6\">\n            <div className=\"flex items-center mb-6\">\n              <User className=\"h-6 w-6 text-gray-600 mr-3\" />\n              <h2 className=\"text-xl font-semibold text-gray-900\">个人资料</h2>\n            </div>\n\n            <form onSubmit={handleProfileSubmit} className=\"space-y-6\">\n              {/* 头像 */}\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                  头像\n                </label>\n                <div className=\"flex items-center space-x-4\">\n                  <div className=\"w-16 h-16 rounded-full bg-gray-300 flex items-center justify-center overflow-hidden\">\n                    {session.user?.image ? (\n                      <img\n                        src={session.user.image}\n                        alt={session.user.name || ''}\n                        className=\"w-full h-full object-cover\"\n                      />\n                    ) : (\n                      <span className=\"text-xl font-medium text-gray-600\">\n                        {session.user?.name?.charAt(0) || 'U'}\n                      </span>\n                    )}\n                  </div>\n                  <div className=\"flex space-x-2\">\n                    <button\n                      type=\"button\"\n                      className=\"inline-flex items-center px-3 py-2 border border-gray-300 text-sm font-medium rounded-lg text-gray-700 bg-white hover:bg-gray-50 transition-colors\"\n                    >\n                      <Camera className=\"mr-2 h-4 w-4\" />\n                      更换头像\n                    </button>\n                    <button\n                      type=\"button\"\n                      className=\"inline-flex items-center px-3 py-2 border border-gray-300 text-sm font-medium rounded-lg text-red-700 bg-white hover:bg-red-50 transition-colors\"\n                    >\n                      <Trash2 className=\"mr-2 h-4 w-4\" />\n                      删除\n                    </button>\n                  </div>\n                </div>\n              </div>\n\n              {/* 基本信息 */}\n              <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n                <div>\n                  <label htmlFor=\"name\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n                    姓名\n                  </label>\n                  <input\n                    type=\"text\"\n                    id=\"name\"\n                    value={profileForm.name}\n                    onChange={(e) => setProfileForm(prev => ({ ...prev, name: e.target.value }))}\n                    className=\"block w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                  />\n                </div>\n\n                <div>\n                  <label htmlFor=\"email\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n                    邮箱\n                  </label>\n                  <input\n                    type=\"email\"\n                    id=\"email\"\n                    value={profileForm.email}\n                    onChange={(e) => setProfileForm(prev => ({ ...prev, email: e.target.value }))}\n                    className=\"block w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                  />\n                </div>\n              </div>\n\n              <div>\n                <label htmlFor=\"bio\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n                  个人简介\n                </label>\n                <textarea\n                  id=\"bio\"\n                  rows={3}\n                  value={profileForm.bio}\n                  onChange={(e) => setProfileForm(prev => ({ ...prev, bio: e.target.value }))}\n                  placeholder=\"介绍一下您自己...\"\n                  className=\"block w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                />\n              </div>\n\n              <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n                <div>\n                  <label htmlFor=\"website\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n                    个人网站\n                  </label>\n                  <input\n                    type=\"url\"\n                    id=\"website\"\n                    value={profileForm.website}\n                    onChange={(e) => setProfileForm(prev => ({ ...prev, website: e.target.value }))}\n                    placeholder=\"https://example.com\"\n                    className=\"block w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                  />\n                </div>\n\n                <div>\n                  <label htmlFor=\"location\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n                    所在地\n                  </label>\n                  <input\n                    type=\"text\"\n                    id=\"location\"\n                    value={profileForm.location}\n                    onChange={(e) => setProfileForm(prev => ({ ...prev, location: e.target.value }))}\n                    placeholder=\"城市, 国家\"\n                    className=\"block w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                  />\n                </div>\n              </div>\n\n              <div className=\"flex justify-end\">\n                <button\n                  type=\"submit\"\n                  disabled={loading}\n                  className=\"inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-lg text-white bg-blue-600 hover:bg-blue-700 disabled:opacity-50 transition-colors\"\n                >\n                  {loading ? (\n                    <LoadingSpinner size=\"sm\" className=\"mr-2\" />\n                  ) : (\n                    <Save className=\"mr-2 h-5 w-5\" />\n                  )}\n                  保存更改\n                </button>\n              </div>\n            </form>\n          </div>\n\n          {/* 通知设置 */}\n          <div className=\"bg-white rounded-lg shadow-sm border border-gray-200 p-6\">\n            <div className=\"flex items-center mb-6\">\n              <Bell className=\"h-6 w-6 text-gray-600 mr-3\" />\n              <h2 className=\"text-xl font-semibold text-gray-900\">通知设置</h2>\n            </div>\n\n            <form onSubmit={handleNotificationSubmit} className=\"space-y-4\">\n              <div className=\"space-y-4\">\n                <div className=\"flex items-center justify-between\">\n                  <div>\n                    <h3 className=\"text-sm font-medium text-gray-900\">邮件通知</h3>\n                    <p className=\"text-sm text-gray-500\">接收重要更新的邮件通知</p>\n                  </div>\n                  <label className=\"relative inline-flex items-center cursor-pointer\">\n                    <input\n                      type=\"checkbox\"\n                      checked={notificationSettings.emailNotifications}\n                      onChange={(e) => setNotificationSettings(prev => ({ ...prev, emailNotifications: e.target.checked }))}\n                      className=\"sr-only peer\"\n                    />\n                    <div className=\"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600\"></div>\n                  </label>\n                </div>\n\n                <div className=\"flex items-center justify-between\">\n                  <div>\n                    <h3 className=\"text-sm font-medium text-gray-900\">工具审核通知</h3>\n                    <p className=\"text-sm text-gray-500\">当您提交的工具审核状态变更时通知您</p>\n                  </div>\n                  <label className=\"relative inline-flex items-center cursor-pointer\">\n                    <input\n                      type=\"checkbox\"\n                      checked={notificationSettings.toolApprovalNotifications}\n                      onChange={(e) => setNotificationSettings(prev => ({ ...prev, toolApprovalNotifications: e.target.checked }))}\n                      className=\"sr-only peer\"\n                    />\n                    <div className=\"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600\"></div>\n                  </label>\n                </div>\n\n                <div className=\"flex items-center justify-between\">\n                  <div>\n                    <h3 className=\"text-sm font-medium text-gray-900\">每周摘要</h3>\n                    <p className=\"text-sm text-gray-500\">接收每周的新工具和热门内容摘要</p>\n                  </div>\n                  <label className=\"relative inline-flex items-center cursor-pointer\">\n                    <input\n                      type=\"checkbox\"\n                      checked={notificationSettings.weeklyDigest}\n                      onChange={(e) => setNotificationSettings(prev => ({ ...prev, weeklyDigest: e.target.checked }))}\n                      className=\"sr-only peer\"\n                    />\n                    <div className=\"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600\"></div>\n                  </label>\n                </div>\n\n                <div className=\"flex items-center justify-between\">\n                  <div>\n                    <h3 className=\"text-sm font-medium text-gray-900\">营销邮件</h3>\n                    <p className=\"text-sm text-gray-500\">接收产品更新和特别优惠信息</p>\n                  </div>\n                  <label className=\"relative inline-flex items-center cursor-pointer\">\n                    <input\n                      type=\"checkbox\"\n                      checked={notificationSettings.marketingEmails}\n                      onChange={(e) => setNotificationSettings(prev => ({ ...prev, marketingEmails: e.target.checked }))}\n                      className=\"sr-only peer\"\n                    />\n                    <div className=\"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600\"></div>\n                  </label>\n                </div>\n              </div>\n\n              <div className=\"flex justify-end pt-4\">\n                <button\n                  type=\"submit\"\n                  disabled={loading}\n                  className=\"inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-lg text-white bg-blue-600 hover:bg-blue-700 disabled:opacity-50 transition-colors\"\n                >\n                  {loading ? (\n                    <LoadingSpinner size=\"sm\" className=\"mr-2\" />\n                  ) : (\n                    <Save className=\"mr-2 h-5 w-5\" />\n                  )}\n                  保存设置\n                </button>\n              </div>\n            </form>\n          </div>\n\n          {/* 隐私设置 */}\n          <div className=\"bg-white rounded-lg shadow-sm border border-gray-200 p-6\">\n            <div className=\"flex items-center mb-6\">\n              <Shield className=\"h-6 w-6 text-gray-600 mr-3\" />\n              <h2 className=\"text-xl font-semibold text-gray-900\">隐私设置</h2>\n            </div>\n\n            <form onSubmit={handlePrivacySubmit} className=\"space-y-6\">\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                  个人资料可见性\n                </label>\n                <select\n                  value={privacySettings.profileVisibility}\n                  onChange={(e) => setPrivacySettings(prev => ({ ...prev, profileVisibility: e.target.value }))}\n                  className=\"block w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                >\n                  <option value=\"public\">公开 - 任何人都可以查看</option>\n                  <option value=\"private\">私密 - 只有您可以查看</option>\n                </select>\n              </div>\n\n              <div className=\"space-y-4\">\n                <div className=\"flex items-center justify-between\">\n                  <div>\n                    <h3 className=\"text-sm font-medium text-gray-900\">显示邮箱地址</h3>\n                    <p className=\"text-sm text-gray-500\">在您的公开资料中显示邮箱地址</p>\n                  </div>\n                  <label className=\"relative inline-flex items-center cursor-pointer\">\n                    <input\n                      type=\"checkbox\"\n                      checked={privacySettings.showEmail}\n                      onChange={(e) => setPrivacySettings(prev => ({ ...prev, showEmail: e.target.checked }))}\n                      className=\"sr-only peer\"\n                    />\n                    <div className=\"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600\"></div>\n                  </label>\n                </div>\n\n                <div className=\"flex items-center justify-between\">\n                  <div>\n                    <h3 className=\"text-sm font-medium text-gray-900\">显示提交的工具</h3>\n                    <p className=\"text-sm text-gray-500\">在您的公开资料中显示您提交的工具</p>\n                  </div>\n                  <label className=\"relative inline-flex items-center cursor-pointer\">\n                    <input\n                      type=\"checkbox\"\n                      checked={privacySettings.showSubmittedTools}\n                      onChange={(e) => setPrivacySettings(prev => ({ ...prev, showSubmittedTools: e.target.checked }))}\n                      className=\"sr-only peer\"\n                    />\n                    <div className=\"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600\"></div>\n                  </label>\n                </div>\n              </div>\n\n              <div className=\"flex justify-end\">\n                <button\n                  type=\"submit\"\n                  disabled={loading}\n                  className=\"inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-lg text-white bg-blue-600 hover:bg-blue-700 disabled:opacity-50 transition-colors\"\n                >\n                  {loading ? (\n                    <LoadingSpinner size=\"sm\" className=\"mr-2\" />\n                  ) : (\n                    <Save className=\"mr-2 h-5 w-5\" />\n                  )}\n                  保存设置\n                </button>\n              </div>\n            </form>\n          </div>\n        </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AATA;;;;;;;;;AAmBe,SAAS;;IACtB,MAAM,EAAE,MAAM,OAAO,EAAE,MAAM,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,iJAAA,CAAA,aAAU,AAAD;IACnD,MAAM,SAAS,CAAA,GAAA,yHAAA,CAAA,YAAS,AAAD;IACvB,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,SAAS;IACT,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QAC7C,MAAM;QACN,OAAO;QACP,KAAK;QACL,SAAS;QACT,UAAU;IACZ;IAEA,OAAO;IACP,MAAM,CAAC,sBAAsB,wBAAwB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QAC/D,oBAAoB;QACpB,2BAA2B;QAC3B,cAAc;QACd,iBAAiB;IACnB;IAEA,OAAO;IACP,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QACrD,mBAAmB;QACnB,WAAW;QACX,oBAAoB;IACtB;IAEA,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;kCAAE;YACR,IAAI,WAAW,mBAAmB;gBAChC,OAAO,IAAI,CAAC;gBACZ;YACF;YAEA,IAAI,WAAW,mBAAmB,SAAS,MAAM;gBAC/C,gBAAgB;gBAChB;YACF;QACF;iCAAG;QAAC;QAAQ;QAAS;KAAO;IAE5B,MAAM,kBAAkB;QACtB,IAAI;YACF,MAAM,WAAW,MAAM,oHAAA,CAAA,YAAS,CAAC,cAAc;YAC/C,IAAI,SAAS,OAAO,IAAI,SAAS,IAAI,EAAE;gBACrC,eAAe;oBACb,MAAM,SAAS,IAAI,CAAC,IAAI,IAAI;oBAC5B,OAAO,SAAS,IAAI,CAAC,KAAK,IAAI;oBAC9B,KAAK,SAAS,IAAI,CAAC,GAAG,IAAI;oBAC1B,SAAS,SAAS,IAAI,CAAC,OAAO,IAAI;oBAClC,UAAU,SAAS,IAAI,CAAC,QAAQ,IAAI;gBACtC;YACF,OAAO;gBACL,4BAA4B;gBAC5B,eAAe;oBACb,MAAM,SAAS,MAAM,QAAQ;oBAC7B,OAAO,SAAS,MAAM,SAAS;oBAC/B,KAAK;oBACL,SAAS;oBACT,UAAU;gBACZ;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,gCAAgC;YAC9C,4BAA4B;YAC5B,eAAe;gBACb,MAAM,SAAS,MAAM,QAAQ;gBAC7B,OAAO,SAAS,MAAM,SAAS;gBAC/B,KAAK;gBACL,SAAS;gBACT,UAAU;YACZ;QACF;IACF;IAEA,MAAM,sBAAsB,OAAO;QACjC,EAAE,cAAc;QAChB,WAAW;QACX,SAAS;QACT,WAAW;QAEX,IAAI;YACF,MAAM,WAAW,MAAM,oHAAA,CAAA,YAAS,CAAC,aAAa,CAAC;gBAC7C,MAAM,YAAY,IAAI;gBACtB,KAAK,YAAY,GAAG;gBACpB,SAAS,YAAY,OAAO;gBAC5B,UAAU,YAAY,QAAQ;YAChC;YAEA,IAAI,SAAS,OAAO,EAAE;gBACpB,YAAY;gBACZ,MAAM,OAAO;oBACX,GAAG,OAAO;oBACV,MAAM;wBACJ,GAAG,SAAS,IAAI;wBAChB,MAAM,YAAY,IAAI;oBACxB;gBACF;gBAEA,WAAW;YACb,OAAO;gBACL,SAAS,SAAS,KAAK,IAAI;YAC7B;QACF,EAAE,OAAO,KAAK;YACZ,QAAQ,KAAK,CAAC,yBAAyB;YACvC,SAAS;QACX,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,2BAA2B,OAAO;QACtC,EAAE,cAAc;QAChB,WAAW;QACX,SAAS;QACT,WAAW;QAEX,IAAI;YACF,oBAAoB;YACpB,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;YACjD,WAAW;QACb,EAAE,OAAO,KAAK;YACZ,SAAS;QACX,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,sBAAsB,OAAO;QACjC,EAAE,cAAc;QAChB,WAAW;QACX,SAAS;QACT,WAAW;QAEX,IAAI;YACF,oBAAoB;YACpB,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;YACjD,WAAW;QACb,EAAE,OAAO,KAAK;YACZ,SAAS;QACX,SAAU;YACR,WAAW;QACb;IACF;IAEA,IAAI,WAAW,WAAW;QACxB,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC,uIAAA,CAAA,UAAc;gBAAC,MAAK;gBAAK,WAAU;;;;;;;;;;;IAG1C;IAEA,IAAI,CAAC,SAAS;QACZ,OAAO;IACT;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEX,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,yHAAA,CAAA,OAAI;wBACH,MAAK;wBACL,WAAU;kCAEV,cAAA,6LAAC,mNAAA,CAAA,YAAS;4BAAC,WAAU;;;;;;;;;;;kCAEvB,6LAAC;;0CACC,6LAAC;gCAAG,WAAU;0CAAmC;;;;;;0CACjD,6LAAC;gCAAE,WAAU;0CAAwB;;;;;;;;;;;;;;;;;;YAKxC,yBACC,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAE,WAAU;8BAAkB;;;;;;;;;;;YAGlC,uBACC,6LAAC,qIAAA,CAAA,UAAY;gBACX,SAAS;gBACT,SAAS,IAAM,SAAS;gBACxB,WAAU;;;;;;0BAId,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,qMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;kDAChB,6LAAC;wCAAG,WAAU;kDAAsC;;;;;;;;;;;;0CAGtD,6LAAC;gCAAK,UAAU;gCAAqB,WAAU;;kDAE7C,6LAAC;;0DACC,6LAAC;gDAAM,WAAU;0DAA+C;;;;;;0DAGhE,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;kEACZ,QAAQ,IAAI,EAAE,sBACb,6LAAC;4DACC,KAAK,QAAQ,IAAI,CAAC,KAAK;4DACvB,KAAK,QAAQ,IAAI,CAAC,IAAI,IAAI;4DAC1B,WAAU;;;;;iFAGZ,6LAAC;4DAAK,WAAU;sEACb,QAAQ,IAAI,EAAE,MAAM,OAAO,MAAM;;;;;;;;;;;kEAIxC,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEACC,MAAK;gEACL,WAAU;;kFAEV,6LAAC,yMAAA,CAAA,SAAM;wEAAC,WAAU;;;;;;oEAAiB;;;;;;;0EAGrC,6LAAC;gEACC,MAAK;gEACL,WAAU;;kFAEV,6LAAC,6MAAA,CAAA,SAAM;wEAAC,WAAU;;;;;;oEAAiB;;;;;;;;;;;;;;;;;;;;;;;;;kDAQ3C,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;;kEACC,6LAAC;wDAAM,SAAQ;wDAAO,WAAU;kEAA+C;;;;;;kEAG/E,6LAAC;wDACC,MAAK;wDACL,IAAG;wDACH,OAAO,YAAY,IAAI;wDACvB,UAAU,CAAC,IAAM,eAAe,CAAA,OAAQ,CAAC;oEAAE,GAAG,IAAI;oEAAE,MAAM,EAAE,MAAM,CAAC,KAAK;gEAAC,CAAC;wDAC1E,WAAU;;;;;;;;;;;;0DAId,6LAAC;;kEACC,6LAAC;wDAAM,SAAQ;wDAAQ,WAAU;kEAA+C;;;;;;kEAGhF,6LAAC;wDACC,MAAK;wDACL,IAAG;wDACH,OAAO,YAAY,KAAK;wDACxB,UAAU,CAAC,IAAM,eAAe,CAAA,OAAQ,CAAC;oEAAE,GAAG,IAAI;oEAAE,OAAO,EAAE,MAAM,CAAC,KAAK;gEAAC,CAAC;wDAC3E,WAAU;;;;;;;;;;;;;;;;;;kDAKhB,6LAAC;;0DACC,6LAAC;gDAAM,SAAQ;gDAAM,WAAU;0DAA+C;;;;;;0DAG9E,6LAAC;gDACC,IAAG;gDACH,MAAM;gDACN,OAAO,YAAY,GAAG;gDACtB,UAAU,CAAC,IAAM,eAAe,CAAA,OAAQ,CAAC;4DAAE,GAAG,IAAI;4DAAE,KAAK,EAAE,MAAM,CAAC,KAAK;wDAAC,CAAC;gDACzE,aAAY;gDACZ,WAAU;;;;;;;;;;;;kDAId,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;;kEACC,6LAAC;wDAAM,SAAQ;wDAAU,WAAU;kEAA+C;;;;;;kEAGlF,6LAAC;wDACC,MAAK;wDACL,IAAG;wDACH,OAAO,YAAY,OAAO;wDAC1B,UAAU,CAAC,IAAM,eAAe,CAAA,OAAQ,CAAC;oEAAE,GAAG,IAAI;oEAAE,SAAS,EAAE,MAAM,CAAC,KAAK;gEAAC,CAAC;wDAC7E,aAAY;wDACZ,WAAU;;;;;;;;;;;;0DAId,6LAAC;;kEACC,6LAAC;wDAAM,SAAQ;wDAAW,WAAU;kEAA+C;;;;;;kEAGnF,6LAAC;wDACC,MAAK;wDACL,IAAG;wDACH,OAAO,YAAY,QAAQ;wDAC3B,UAAU,CAAC,IAAM,eAAe,CAAA,OAAQ,CAAC;oEAAE,GAAG,IAAI;oEAAE,UAAU,EAAE,MAAM,CAAC,KAAK;gEAAC,CAAC;wDAC9E,aAAY;wDACZ,WAAU;;;;;;;;;;;;;;;;;;kDAKhB,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CACC,MAAK;4CACL,UAAU;4CACV,WAAU;;gDAET,wBACC,6LAAC,uIAAA,CAAA,UAAc;oDAAC,MAAK;oDAAK,WAAU;;;;;yEAEpC,6LAAC,qMAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;gDAChB;;;;;;;;;;;;;;;;;;;;;;;;kCAQV,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,qMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;kDAChB,6LAAC;wCAAG,WAAU;kDAAsC;;;;;;;;;;;;0CAGtD,6LAAC;gCAAK,UAAU;gCAA0B,WAAU;;kDAClD,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;;0EACC,6LAAC;gEAAG,WAAU;0EAAoC;;;;;;0EAClD,6LAAC;gEAAE,WAAU;0EAAwB;;;;;;;;;;;;kEAEvC,6LAAC;wDAAM,WAAU;;0EACf,6LAAC;gEACC,MAAK;gEACL,SAAS,qBAAqB,kBAAkB;gEAChD,UAAU,CAAC,IAAM,wBAAwB,CAAA,OAAQ,CAAC;4EAAE,GAAG,IAAI;4EAAE,oBAAoB,EAAE,MAAM,CAAC,OAAO;wEAAC,CAAC;gEACnG,WAAU;;;;;;0EAEZ,6LAAC;gEAAI,WAAU;;;;;;;;;;;;;;;;;;0DAInB,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;;0EACC,6LAAC;gEAAG,WAAU;0EAAoC;;;;;;0EAClD,6LAAC;gEAAE,WAAU;0EAAwB;;;;;;;;;;;;kEAEvC,6LAAC;wDAAM,WAAU;;0EACf,6LAAC;gEACC,MAAK;gEACL,SAAS,qBAAqB,yBAAyB;gEACvD,UAAU,CAAC,IAAM,wBAAwB,CAAA,OAAQ,CAAC;4EAAE,GAAG,IAAI;4EAAE,2BAA2B,EAAE,MAAM,CAAC,OAAO;wEAAC,CAAC;gEAC1G,WAAU;;;;;;0EAEZ,6LAAC;gEAAI,WAAU;;;;;;;;;;;;;;;;;;0DAInB,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;;0EACC,6LAAC;gEAAG,WAAU;0EAAoC;;;;;;0EAClD,6LAAC;gEAAE,WAAU;0EAAwB;;;;;;;;;;;;kEAEvC,6LAAC;wDAAM,WAAU;;0EACf,6LAAC;gEACC,MAAK;gEACL,SAAS,qBAAqB,YAAY;gEAC1C,UAAU,CAAC,IAAM,wBAAwB,CAAA,OAAQ,CAAC;4EAAE,GAAG,IAAI;4EAAE,cAAc,EAAE,MAAM,CAAC,OAAO;wEAAC,CAAC;gEAC7F,WAAU;;;;;;0EAEZ,6LAAC;gEAAI,WAAU;;;;;;;;;;;;;;;;;;0DAInB,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;;0EACC,6LAAC;gEAAG,WAAU;0EAAoC;;;;;;0EAClD,6LAAC;gEAAE,WAAU;0EAAwB;;;;;;;;;;;;kEAEvC,6LAAC;wDAAM,WAAU;;0EACf,6LAAC;gEACC,MAAK;gEACL,SAAS,qBAAqB,eAAe;gEAC7C,UAAU,CAAC,IAAM,wBAAwB,CAAA,OAAQ,CAAC;4EAAE,GAAG,IAAI;4EAAE,iBAAiB,EAAE,MAAM,CAAC,OAAO;wEAAC,CAAC;gEAChG,WAAU;;;;;;0EAEZ,6LAAC;gEAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;;kDAKrB,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CACC,MAAK;4CACL,UAAU;4CACV,WAAU;;gDAET,wBACC,6LAAC,uIAAA,CAAA,UAAc;oDAAC,MAAK;oDAAK,WAAU;;;;;yEAEpC,6LAAC,qMAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;gDAChB;;;;;;;;;;;;;;;;;;;;;;;;kCAQV,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,yMAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;kDAClB,6LAAC;wCAAG,WAAU;kDAAsC;;;;;;;;;;;;0CAGtD,6LAAC;gCAAK,UAAU;gCAAqB,WAAU;;kDAC7C,6LAAC;;0DACC,6LAAC;gDAAM,WAAU;0DAA+C;;;;;;0DAGhE,6LAAC;gDACC,OAAO,gBAAgB,iBAAiB;gDACxC,UAAU,CAAC,IAAM,mBAAmB,CAAA,OAAQ,CAAC;4DAAE,GAAG,IAAI;4DAAE,mBAAmB,EAAE,MAAM,CAAC,KAAK;wDAAC,CAAC;gDAC3F,WAAU;;kEAEV,6LAAC;wDAAO,OAAM;kEAAS;;;;;;kEACvB,6LAAC;wDAAO,OAAM;kEAAU;;;;;;;;;;;;;;;;;;kDAI5B,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;;0EACC,6LAAC;gEAAG,WAAU;0EAAoC;;;;;;0EAClD,6LAAC;gEAAE,WAAU;0EAAwB;;;;;;;;;;;;kEAEvC,6LAAC;wDAAM,WAAU;;0EACf,6LAAC;gEACC,MAAK;gEACL,SAAS,gBAAgB,SAAS;gEAClC,UAAU,CAAC,IAAM,mBAAmB,CAAA,OAAQ,CAAC;4EAAE,GAAG,IAAI;4EAAE,WAAW,EAAE,MAAM,CAAC,OAAO;wEAAC,CAAC;gEACrF,WAAU;;;;;;0EAEZ,6LAAC;gEAAI,WAAU;;;;;;;;;;;;;;;;;;0DAInB,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;;0EACC,6LAAC;gEAAG,WAAU;0EAAoC;;;;;;0EAClD,6LAAC;gEAAE,WAAU;0EAAwB;;;;;;;;;;;;kEAEvC,6LAAC;wDAAM,WAAU;;0EACf,6LAAC;gEACC,MAAK;gEACL,SAAS,gBAAgB,kBAAkB;gEAC3C,UAAU,CAAC,IAAM,mBAAmB,CAAA,OAAQ,CAAC;4EAAE,GAAG,IAAI;4EAAE,oBAAoB,EAAE,MAAM,CAAC,OAAO;wEAAC,CAAC;gEAC9F,WAAU;;;;;;0EAEZ,6LAAC;gEAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;;kDAKrB,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CACC,MAAK;4CACL,UAAU;4CACV,WAAU;;gDAET,wBACC,6LAAC,uIAAA,CAAA,UAAc;oDAAC,MAAK;oDAAK,WAAU;;;;;yEAEpC,6LAAC,qMAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;gDAChB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASpB;GA5ewB;;QACoB,iJAAA,CAAA,aAAU;QACrC,yHAAA,CAAA,YAAS;;;KAFF", "debugId": null}}]}