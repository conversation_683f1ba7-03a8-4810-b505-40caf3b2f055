{"sorted_middleware": [], "middleware": {"/": {"files": ["server/edge/chunks/_915e9f02._.js", "server/edge/chunks/node_modules_@formatjs_intl-localematcher_lib_f4580406._.js", "server/edge/chunks/node_modules_4ac6bddb._.js", "server/edge/chunks/[root-of-the-server]__e8693e40._.js", "server/edge/chunks/edge-wrapper_ff61254e.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "/:nextData(_next/data/[^/]{1,})?/((?!api|_next/static|_next/image|favicon.ico|robots.txt|sitemap.xml|uploads).*){(\\\\.json)}?", "originalSource": "/((?!api|_next/static|_next/image|favicon.ico|robots.txt|sitemap.xml|uploads).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "pbGO3VwpkSvUolI25fpFPUUkncDK/RUErZt9oTegUAc=", "__NEXT_PREVIEW_MODE_ID": "abf87b7f777b2a4f465dc6ab982b3c3a", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "601d2a00c9112f77fc0df3617ecbd0002c694a9e0463ab15d1059857d01657b4", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "48be6d92b8e7b17ce674a1b5927f7e7db3ded8898bf48a5832e7d7c246e041c0"}}}, "instrumentation": null, "functions": {}}