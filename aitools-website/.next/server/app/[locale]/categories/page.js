(()=>{var e={};e.id=5534,e.ids=[5534],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19995:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>f,dynamic:()=>h,generateMetadata:()=>g});var s=r(37413);r(61120);var a=r(64348),o=r(78878),i=r(42964),l=r(56333),n=r(26373);let c=(0,n.A)("grid-3x3",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",key:"afitv7"}],["path",{d:"M3 9h18",key:"1pudct"}],["path",{d:"M3 15h18",key:"5xshup"}],["path",{d:"M9 3v18",key:"fh3hqa"}],["path",{d:"M15 3v18",key:"14nvp0"}]]),d=(0,n.A)("trending-up",[["path",{d:"M16 7h6v6",key:"box55l"}],["path",{d:"m22 7-8.5 8.5-5-5L2 17",key:"1t1m79"}]]);async function m({categories:e,error:t,locale:r}){let n=await (0,a.A)({locale:r,namespace:"categories_page"});if(t)return(0,s.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:(0,s.jsx)(l.default,{message:t})});let m=e.sort((e,t)=>t.toolCount-e.toolCount).slice(0,6);return(0,s.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[(0,s.jsxs)("div",{className:"text-center mb-12",children:[(0,s.jsxs)("h1",{className:"text-4xl font-bold text-gray-900 mb-4",children:[(0,s.jsx)(c,{className:"inline-block mr-3 h-10 w-10 text-blue-600"}),n("title")]}),(0,s.jsx)("p",{className:"text-xl text-gray-600 max-w-3xl mx-auto",children:n("subtitle")})]}),(0,s.jsx)("div",{className:"bg-gradient-to-r from-blue-50 to-indigo-100 rounded-lg p-8 mb-12",children:(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6 text-center",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("div",{className:"text-3xl font-bold text-blue-600 mb-2",children:e.length}),(0,s.jsx)("div",{className:"text-gray-700",children:n("categories_count")})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("div",{className:"text-3xl font-bold text-blue-600 mb-2",children:e.reduce((e,t)=>e+t.toolCount,0)}),(0,s.jsx)("div",{className:"text-gray-700",children:n("tools_count")})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("div",{className:"text-3xl font-bold text-blue-600 mb-2",children:e.length>0?Math.round(e.reduce((e,t)=>e+t.toolCount,0)/e.length):0}),(0,s.jsx)("div",{className:"text-gray-700",children:n("avg_tools_per_category")})]})]})}),(0,s.jsxs)("section",{className:"mb-16",children:[(0,s.jsxs)("div",{className:"flex items-center mb-8",children:[(0,s.jsx)(d,{className:"h-8 w-8 text-blue-600 mr-3"}),(0,s.jsx)("h2",{className:"text-3xl font-bold text-gray-900",children:n("popular_categories")})]}),(0,s.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:m.map(e=>(0,s.jsx)(i.default,{category:e},e._id))})]}),(0,s.jsxs)("section",{children:[(0,s.jsxs)("div",{className:"flex items-center justify-between mb-8",children:[(0,s.jsx)("h2",{className:"text-3xl font-bold text-gray-900",children:n("all_categories")}),(0,s.jsx)("div",{className:"text-sm text-gray-600",children:n("categories_total",{count:e.length})})]}),(0,s.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6",children:e.map(e=>(0,s.jsx)(i.default,{category:e},e._id))})]}),(0,s.jsxs)("section",{className:"mt-16 bg-blue-600 rounded-lg p-8 text-center",children:[(0,s.jsx)("h2",{className:"text-2xl font-bold text-white mb-4",children:n("not_found_title")}),(0,s.jsx)("p",{className:"text-blue-100 mb-6",children:n("not_found_desc")}),(0,s.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4 justify-center",children:[(0,s.jsx)(o.N_,{href:"/submit",className:"inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-lg text-blue-600 bg-white hover:bg-gray-50 transition-colors",children:n("submit_tool")}),(0,s.jsx)(o.N_,{href:"/contact",className:"inline-flex items-center px-6 py-3 border border-white text-base font-medium rounded-lg text-white hover:bg-blue-700 transition-colors",children:n("contact_us")})]})]})]})}var x=r(60366),p=r(10806),u=r(54290);let h="force-dynamic";async function g({params:e}){let{locale:t}=await e,r=await (0,a.A)({locale:t,namespace:"categories"});try{let e=await p.u.getCategories(),s=e.success&&e.data?e.data.categories.length:0,a=e.success&&e.data?e.data.overview.totalTools:0,o=r("page_title"),i=r("page_description",{categories:s,tools:a}),l=r("page_keywords");return{title:o,description:i,keywords:l,authors:[{name:"zh"===t?"AI工具导航团队":"AI Tools Directory Team"}],robots:{index:!0,follow:!0},openGraph:{type:"website",locale:"zh"===t?"zh_CN":"en_US",url:`${process.env.NEXT_PUBLIC_BASE_URL||"https://aitools.example.com"}/${t}/categories`,siteName:r("site_name"),title:o,description:i,images:[{url:"/og-categories.jpg",width:1200,height:630,alt:o}]},twitter:{card:"summary_large_image",title:o,description:i,images:["/og-categories.jpg"]},alternates:{canonical:`${process.env.NEXT_PUBLIC_BASE_URL||"https://aitools.example.com"}/${t}/categories`}}}catch(e){return{title:"zh"===t?"AI工具分类 - 按功能浏览人工智能工具":"AI Tools Categories - Browse AI Tools by Function",description:"zh"===t?"按功能分类浏览AI工具，包括文本生成、图像创作、数据分析、自动化、音频处理等各类人工智能工具分类。":"Browse AI tools by function categories, including text generation, image creation, data analysis, automation, audio processing and other AI tool categories.",keywords:"zh"===t?"AI工具分类,人工智能分类,AI工具类别,机器学习分类,深度学习工具分类,AI应用分类,智能工具分类":"AI tools categories,artificial intelligence categories,AI tool types,machine learning categories,deep learning tool categories,AI application categories,smart tool categories"}}}async function b(e="en"){try{let t=await (0,a.A)({locale:e,namespace:"categories"}),r=await p.u.getCategories();if(r.success&&r.data)return{categories:r.data.categories.map(e=>{let r=x.RI[e.id]||{description:"优质AI工具集合",icon:"\uD83D\uDD27",color:"#6B7280"};return{_id:e.id,name:t(`category_names.${e.id}`),slug:e.id,description:t(`category_descriptions.${e.id}`),icon:r.icon,color:r.color,toolCount:e.count}}),error:null};return{categories:[],error:r.error||"Failed to fetch categories list"}}catch(e){return console.error("Failed to fetch categories:",e),{categories:[],error:"Failed to fetch categories list, please try again later"}}}async function f({params:e}){let{locale:t}=await e,{categories:r,error:o}=await b(t),i=await (0,a.A)({locale:t,namespace:"categories"}),l=(0,u.hC)([{name:i("breadcrumb_home"),url:"/"},{name:i("breadcrumb_categories"),url:"/categories"}]),n={"@context":"https://schema.org","@type":"ItemList",name:i("structured_data_name"),description:i("structured_data_description"),numberOfItems:r.length,itemListElement:r.map((e,r)=>({"@type":"ListItem",position:r+1,item:{"@type":"Thing",name:e.name,description:e.description,url:`${process.env.NEXT_PUBLIC_BASE_URL||"https://aitools.example.com"}/${t}/categories/${e.slug}`,additionalProperty:{"@type":"PropertyValue",name:i("structured_data_tool_count"),value:e.toolCount}}}))};return(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("script",{type:"application/ld+json",dangerouslySetInnerHTML:{__html:JSON.stringify(l)}}),(0,s.jsx)("script",{type:"application/ld+json",dangerouslySetInnerHTML:{__html:JSON.stringify(n)}}),(0,s.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4",children:(0,s.jsx)("nav",{className:"flex","aria-label":i("breadcrumb_aria_label"),children:(0,s.jsxs)("ol",{className:"inline-flex items-center space-x-1 md:space-x-3",children:[(0,s.jsx)("li",{className:"inline-flex items-center",children:(0,s.jsx)("a",{href:"/",className:"inline-flex items-center text-sm font-medium text-gray-700 hover:text-blue-600",children:i("breadcrumb_home")})}),(0,s.jsx)("li",{children:(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)("svg",{className:"w-6 h-6 text-gray-400",fill:"currentColor",viewBox:"0 0 20 20",children:(0,s.jsx)("path",{fillRule:"evenodd",d:"M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z",clipRule:"evenodd"})}),(0,s.jsx)("span",{className:"ml-1 text-sm font-medium text-gray-500 md:ml-2",children:i("breadcrumb_categories")})]})})]})})}),(0,s.jsx)(m,{categories:r,error:o,locale:t})]})}},24222:(e,t,r)=>{"use strict";r.d(t,{default:()=>i});var s=r(60687);r(43210);var a=r(12340),o=r(77618);let i=({category:e})=>{let t=(0,o.c3)("common");return(0,s.jsx)(a.N_,{href:`/categories/${e.slug}`,children:(0,s.jsx)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 hover:shadow-md transition-all duration-200 hover:scale-105 cursor-pointer",style:{height:"100%"},children:(0,s.jsxs)("div",{className:"p-6",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-4 mb-4",children:[(0,s.jsx)("div",{className:"w-12 h-12 rounded-lg flex items-center justify-center text-2xl",style:{backgroundColor:e.color||"#3B82F6"},children:(0,s.jsx)("span",{className:"text-white",children:e.icon||"\uD83D\uDD27"})}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-1",children:e.name}),(0,s.jsx)("p",{className:"text-sm text-gray-500",children:t("tools_count",{count:e.toolCount})})]})]}),(0,s.jsx)("p",{className:"text-gray-600 text-sm",children:e.description})]})})})}},27370:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>i.a,__next_app__:()=>m,pages:()=>d,routeModule:()=>x,tree:()=>c});var s=r(65239),a=r(48088),o=r(88170),i=r.n(o),l=r(30893),n={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(n[e]=()=>l[e]);r.d(t,n);let c={children:["",{children:["[locale]",{children:["categories",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,19995)),"/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/categories/page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,32410)),"/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"/Users/<USER>/workspace/aitools/aitools-website/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,d=["/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/categories/page.tsx"],m={require:r,loadChunk:()=>Promise.resolve()},x=new s.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/[locale]/categories/page",pathname:"/[locale]/categories",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},38284:(e,t,r)=>{Promise.resolve().then(r.bind(r,39130)),Promise.resolve().then(r.bind(r,45196)),Promise.resolve().then(r.bind(r,24222)),Promise.resolve().then(r.bind(r,11011))},42964:(e,t,r)=>{"use strict";r.d(t,{default:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/workspace/aitools/aitools-website/src/components/CategoryCard.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/workspace/aitools/aitools-website/src/components/CategoryCard.tsx","default")},51436:(e,t,r)=>{Promise.resolve().then(r.bind(r,75788)),Promise.resolve().then(r.bind(r,80994)),Promise.resolve().then(r.bind(r,42964)),Promise.resolve().then(r.bind(r,56333))},62688:(e,t,r)=>{"use strict";r.d(t,{A:()=>m});var s=r(43210);let a=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),o=e=>e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,t,r)=>r?r.toUpperCase():t.toLowerCase()),i=e=>{let t=o(e);return t.charAt(0).toUpperCase()+t.slice(1)},l=(...e)=>e.filter((e,t,r)=>!!e&&""!==e.trim()&&r.indexOf(e)===t).join(" ").trim(),n=e=>{for(let t in e)if(t.startsWith("aria-")||"role"===t||"title"===t)return!0};var c={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let d=(0,s.forwardRef)(({color:e="currentColor",size:t=24,strokeWidth:r=2,absoluteStrokeWidth:a,className:o="",children:i,iconNode:d,...m},x)=>(0,s.createElement)("svg",{ref:x,...c,width:t,height:t,stroke:e,strokeWidth:a?24*Number(r)/Number(t):r,className:l("lucide",o),...!i&&!n(m)&&{"aria-hidden":"true"},...m},[...d.map(([e,t])=>(0,s.createElement)(e,t)),...Array.isArray(i)?i:[i]])),m=(e,t)=>{let r=(0,s.forwardRef)(({className:r,...o},n)=>(0,s.createElement)(d,{ref:n,iconNode:t,className:l(`lucide-${a(i(e))}`,`lucide-${e}`,r),...o}));return r.displayName=i(e),r}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},79551:e=>{"use strict";e.exports=require("url")}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[4243,4999,9658,6435,6699,8232,2585,7702],()=>r(27370));module.exports=s})();