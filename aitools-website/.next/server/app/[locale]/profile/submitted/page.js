(()=>{var e={};e.id=1569,e.ids=[1569],e.modules={2777:(e,t,s)=>{"use strict";s.d(t,{default:()=>r});let r=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/workspace/aitools/aitools-website/src/components/profile/SubmittedToolsListClient.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/workspace/aitools/aitools-website/src/components/profile/SubmittedToolsListClient.tsx","default")},2952:(e,t,s)=>{"use strict";s.d(t,{iu:()=>n,ng:()=>o,Ay:()=>l});var r=s(60687);s(43210);var i=s(31261),a=s.n(i);function l({src:e,alt:t,width:s,height:i,className:l="",priority:n=!1,fill:o=!1,sizes:d,placeholder:c="empty",blurDataURL:m,fallbackSrc:u="/images/placeholder.svg"}){let p={src:e,alt:t,className:l,priority:n,placeholder:"blur"===c?"blur":"empty",blurDataURL:m||("blur"===c?"data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTAwIiBoZWlnaHQ9IjEwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KICA8ZGVmcz4KICAgIDxsaW5lYXJHcmFkaWVudCBpZD0iZ3JhZGllbnQiIHgxPSIwJSIgeTE9IjAlIiB4Mj0iMTAwJSIgeTI9IjEwMCUiPgogICAgICA8c3RvcCBvZmZzZXQ9IjAlIiBzdHlsZT0ic3RvcC1jb2xvcjojZjNmNGY2O3N0b3Atb3BhY2l0eToxIiAvPgogICAgICA8c3RvcCBvZmZzZXQ9IjEwMCUiIHN0eWxlPSJzdG9wLWNvbG9yOiNlNWU3ZWI7c3RvcC1vcGFjaXR5OjEiIC8+CiAgICA8L2xpbmVhckdyYWRpZW50PgogIDwvZGVmcz4KICA8cmVjdCB3aWR0aD0iMTAwIiBoZWlnaHQ9IjEwMCIgZmlsbD0idXJsKCNncmFkaWVudCkiIC8+Cjwvc3ZnPg==":void 0),sizes:d||(o?"100vw":void 0)};return o?(0,r.jsx)("div",{className:"relative overflow-hidden",children:(0,r.jsx)(a(),{...p,fill:!0,style:{objectFit:"cover"}})}):(0,r.jsx)(a(),{...p,width:s,height:i})}let n={avatar:{width:40,height:40},avatarLarge:{width:80,height:80},toolLogo:{width:52,height:52},toolLogoLarge:{width:128,height:128},thumbnail:{width:200,height:150},card:{width:300,height:200},hero:{width:1200,height:600}},o={avatar:"40px",toolLogo:"52px",thumbnail:"(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw",card:"(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw",hero:"100vw",full:"100vw"}},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},5336:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(62688).A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},7211:(e,t,s)=>{"use strict";s.d(t,{default:()=>j});var r=s(60687),i=s(43210),a=s(12340),l=s(78521),n=s(77618),o=s(11011),d=s(5336),c=s(48730),m=s(35071),u=s(63143),p=s(28559),x=s(96474),h=s(53411),g=s(13861),v=s(40228),f=s(25334),y=s(2952);let b=e=>{switch(e){case"approved":return"bg-green-100 text-green-800";case"pending":return"bg-yellow-100 text-yellow-800";case"rejected":return"bg-red-100 text-red-800";default:return"bg-gray-100 text-gray-800"}},w=e=>{switch(e){case"approved":return(0,r.jsx)(d.A,{className:"h-4 w-4"});case"pending":return(0,r.jsx)(c.A,{className:"h-4 w-4"});case"rejected":return(0,r.jsx)(m.A,{className:"h-4 w-4"});case"draft":return(0,r.jsx)(u.A,{className:"h-4 w-4"});default:return null}};function j({initialTools:e,initialStats:t}){(0,a.rd)();let s=(0,l.Ym)(),c=(0,n.c3)("profile.submitted"),[m,j]=(0,i.useState)("all"),[N,A]=(0,i.useState)(e),[_,k]=(0,i.useState)(""),S=e=>c(`status.${e}`),I=N.filter(e=>"all"===m||e.status===m),C={total:N.length,draft:N.filter(e=>"draft"===e.status).length,approved:N.filter(e=>"approved"===e.status).length,pending:N.filter(e=>"pending"===e.status).length,rejected:N.filter(e=>"rejected"===e.status).length,totalViews:N.reduce((e,t)=>e+(t.views||0),0),totalLikes:N.reduce((e,t)=>e+(t.likes||0),0)};return(0,r.jsx)(i.Fragment,{children:(0,r.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[(0,r.jsxs)("div",{className:"flex flex-col sm:flex-row sm:items-center sm:justify-between mb-8",children:[(0,r.jsxs)("div",{children:[(0,r.jsxs)("div",{className:"flex items-center mb-2",children:[(0,r.jsx)(a.N_,{href:"/profile",className:"mr-4 p-2 text-gray-400 hover:text-gray-600 transition-colors",children:(0,r.jsx)(p.A,{className:"h-5 w-5"})}),(0,r.jsx)("h1",{className:"text-3xl font-bold text-gray-900",children:c("title")})]}),(0,r.jsx)("p",{className:"text-lg text-gray-600",children:c("subtitle")})]}),(0,r.jsxs)(a.N_,{href:"/submit",className:"inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-lg text-white bg-blue-600 hover:bg-blue-700 transition-colors",children:[(0,r.jsx)(x.A,{className:"mr-2 h-5 w-5"}),c("submit_new_tool")]})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8",children:[(0,r.jsx)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("div",{className:"flex-shrink-0",children:(0,r.jsx)(h.A,{className:"h-8 w-8 text-blue-600"})}),(0,r.jsxs)("div",{className:"ml-4",children:[(0,r.jsx)("p",{className:"text-sm font-medium text-gray-500",children:c("stats.total_submissions")}),(0,r.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:C.total})]})]})}),(0,r.jsx)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("div",{className:"flex-shrink-0",children:(0,r.jsx)(d.A,{className:"h-8 w-8 text-green-600"})}),(0,r.jsxs)("div",{className:"ml-4",children:[(0,r.jsx)("p",{className:"text-sm font-medium text-gray-500",children:c("stats.approved")}),(0,r.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:C.approved})]})]})}),(0,r.jsx)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("div",{className:"flex-shrink-0",children:(0,r.jsx)(g.A,{className:"h-8 w-8 text-purple-600"})}),(0,r.jsxs)("div",{className:"ml-4",children:[(0,r.jsx)("p",{className:"text-sm font-medium text-gray-500",children:c("stats.total_views")}),(0,r.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:C.totalViews})]})]})}),(0,r.jsx)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("div",{className:"flex-shrink-0",children:(0,r.jsx)("div",{className:"h-8 w-8 text-red-600",children:"❤️"})}),(0,r.jsxs)("div",{className:"ml-4",children:[(0,r.jsx)("p",{className:"text-sm font-medium text-gray-500",children:c("stats.total_likes")}),(0,r.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:C.totalLikes})]})]})})]}),(0,r.jsx)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6",children:(0,r.jsxs)("div",{className:"flex flex-wrap gap-2",children:[(0,r.jsxs)("button",{onClick:()=>j("all"),className:`px-4 py-2 rounded-lg text-sm font-medium transition-colors ${"all"===m?"bg-blue-600 text-white":"bg-gray-100 text-gray-700 hover:bg-gray-200"}`,children:[c("filters.all")," (",C.total,")"]}),(0,r.jsxs)("button",{onClick:()=>j("draft"),className:`px-4 py-2 rounded-lg text-sm font-medium transition-colors ${"draft"===m?"bg-gray-600 text-white":"bg-gray-100 text-gray-700 hover:bg-gray-200"}`,children:[c("filters.draft")," (",C.draft,")"]}),(0,r.jsxs)("button",{onClick:()=>j("approved"),className:`px-4 py-2 rounded-lg text-sm font-medium transition-colors ${"approved"===m?"bg-green-600 text-white":"bg-gray-100 text-gray-700 hover:bg-gray-200"}`,children:[c("filters.approved")," (",C.approved,")"]}),(0,r.jsxs)("button",{onClick:()=>j("pending"),className:`px-4 py-2 rounded-lg text-sm font-medium transition-colors ${"pending"===m?"bg-yellow-600 text-white":"bg-gray-100 text-gray-700 hover:bg-gray-200"}`,children:[c("filters.pending")," (",C.pending,")"]}),(0,r.jsxs)("button",{onClick:()=>j("rejected"),className:`px-4 py-2 rounded-lg text-sm font-medium transition-colors ${"rejected"===m?"bg-red-600 text-white":"bg-gray-100 text-gray-700 hover:bg-gray-200"}`,children:[c("filters.rejected")," (",C.rejected,")"]})]})}),_&&(0,r.jsx)(o.default,{message:_,onClose:()=>k(""),className:"mb-6"}),(0,r.jsx)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200",children:I.length>0?(0,r.jsx)("div",{className:"divide-y divide-gray-200",children:I.map(e=>(0,r.jsx)("div",{className:"p-6",children:(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsxs)("div",{className:"flex-1",children:[(0,r.jsx)("div",{className:"flex items-center space-x-4",children:(0,r.jsxs)("div",{className:"flex flex-col",children:[e.logo&&(0,r.jsx)(y.Ay,{src:e.logo,alt:`${e.name} logo`,width:y.iu.toolLogo.width,height:y.iu.toolLogo.height,className:"rounded-lg object-cover",sizes:y.ng.toolLogo,placeholder:"blur"}),(0,r.jsxs)("div",{className:"flex items-center space-x-3 mb-2",children:[(0,r.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:e.name}),(0,r.jsxs)("span",{className:`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${b(e.status)}`,children:[w(e.status),(0,r.jsx)("span",{className:"ml-1",children:S(e.status)})]})]}),(0,r.jsx)("p",{className:"text-gray-500 mb-3 line-clamp-2",children:e.tagline})]})}),(0,r.jsx)("p",{className:"text-gray-700 mb-3 line-clamp-2",children:e.description}),(0,r.jsxs)("div",{className:"flex items-center space-x-6 text-sm text-gray-500",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,r.jsx)(v.A,{className:"h-4 w-4"}),(0,r.jsxs)("span",{children:[c("dates.submitted_on")," ",new Date(e.submittedAt).toLocaleDateString("zh"===s?"zh-CN":"en-US")]})]}),e.launchDate&&(0,r.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,r.jsx)(d.A,{className:"h-4 w-4"}),(0,r.jsxs)("span",{children:[c("dates.published_on")," ",new Date(e.launchDate).toLocaleDateString("zh"===s?"zh-CN":"en-US")]})]}),"approved"===e.status&&(0,r.jsxs)(r.Fragment,{children:[(0,r.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,r.jsx)(g.A,{className:"h-4 w-4"}),(0,r.jsxs)("span",{children:[e.views||0," ",c("metrics.views")]})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,r.jsx)("span",{children:"❤️"}),(0,r.jsxs)("span",{children:[e.likes||0," ",c("metrics.likes")]})]})]})]}),"rejected"===e.status&&(0,r.jsxs)("div",{className:"mt-3 p-3 bg-red-50 border border-red-200 rounded-lg",children:[e.reviewNotes&&(0,r.jsxs)("p",{className:"text-sm text-red-800 mb-3",children:[(0,r.jsx)("strong",{children:c("rejection.reason")})," ",e.reviewNotes]}),(0,r.jsxs)("div",{className:"flex justify-between items-center",children:[(0,r.jsx)("p",{className:"text-sm text-red-700",children:c("rejection.edit_and_reapply")}),(0,r.jsx)("div",{className:"flex space-x-2",children:(0,r.jsxs)(a.N_,{href:`/submit/edit/${e._id}`,className:"inline-flex items-center px-3 py-1 bg-blue-600 text-white text-xs rounded-lg hover:bg-blue-700 transition-colors",children:[(0,r.jsx)(u.A,{className:"h-3 w-3 mr-1"}),c("actions.edit_info")]})})]})]}),"draft"===e.status&&(0,r.jsxs)("div",{className:"mt-3 p-3 bg-blue-50 border border-blue-200 rounded-lg flex justify-between items-center",children:[(0,r.jsx)("p",{className:"text-sm text-blue-800",children:(0,r.jsx)("strong",{children:c("next_steps.select_launch_date")})}),(0,r.jsx)(a.N_,{href:`/submit/launch-date/${e._id}`,className:"inline-flex items-center px-3 py-1 bg-blue-600 text-white text-xs rounded-lg hover:bg-blue-700 transition-colors",children:c("actions.set_launch_date")})]}),"pending"===e.status&&e.launchOption&&(0,r.jsx)("div",{className:"mt-3 p-3 bg-yellow-50 border border-yellow-200 rounded-lg",children:(0,r.jsxs)("div",{className:"text-sm text-yellow-800 flex justify-between items-center",children:[(0,r.jsxs)("div",{className:"flex justify-between items-center",children:[(0,r.jsx)("span",{children:(0,r.jsx)("strong",{children:c("next_steps.launch_option")})}),(0,r.jsx)("span",{className:`px-2 py-1 rounded text-xs ${"paid"===e.launchOption?"bg-purple-100 text-purple-800":"bg-green-100 text-green-800"}`,children:c(`launch_options.${e.launchOption}`)})]}),"paid"===e.launchOption&&(0,r.jsxs)("div",{className:"flex justify-between items-center",children:[(0,r.jsx)("span",{children:(0,r.jsx)("strong",{children:c("next_steps.payment_status")})}),(0,r.jsx)("span",{className:`px-2 py-1 rounded text-xs ${"completed"===e.paymentStatus?"bg-green-100 text-green-800":"bg-yellow-100 text-yellow-800"}`,children:c(`payment_status.${e.paymentStatus}`)})]}),e.selectedLaunchDate&&(0,r.jsxs)("div",{className:"flex justify-between items-center",children:[(0,r.jsx)("span",{children:(0,r.jsxs)("strong",{children:[c("dates.planned_launch"),":"]})}),(0,r.jsx)("span",{children:new Date(e.selectedLaunchDate).toLocaleDateString("zh"===s?"zh-CN":"en-US")})]}),(0,r.jsx)("div",{className:"flex justify-end",children:(0,r.jsxs)(a.N_,{href:`/submit/launch-date/${e._id}?mode=edit`,className:"inline-flex items-center px-3 py-1 bg-yellow-600 text-white text-xs rounded-lg hover:bg-yellow-700 transition-colors",children:[(0,r.jsx)(v.A,{className:"h-3 w-3 mr-1"}),c("actions.modify_launch_date")]})})]})}),"approved"===e.status&&e.launchOption&&(0,r.jsx)("div",{className:"mt-3 p-3 bg-green-50 border border-green-200 rounded-lg",children:(0,r.jsxs)("div",{className:"text-sm text-green-800 flex justify-between items-center",children:[(0,r.jsxs)("div",{className:"flex justify-between items-center",children:[(0,r.jsx)("span",{children:(0,r.jsx)("strong",{children:c("next_steps.launch_option")})}),(0,r.jsx)("span",{className:`px-2 py-1 rounded text-xs ${"paid"===e.launchOption?"bg-purple-100 text-purple-800":"bg-green-100 text-green-800"}`,children:c(`launch_options.${e.launchOption}`)})]}),e.selectedLaunchDate&&(0,r.jsxs)("div",{className:"flex justify-between items-center",children:[(0,r.jsx)("span",{children:(0,r.jsx)("strong",{children:c("dates.launch_date")})}),(0,r.jsx)("span",{children:new Date(e.selectedLaunchDate).toLocaleDateString("zh"===s?"zh-CN":"en-US")})]})]})})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-2 ml-4 absolute right-0 top-0",children:["approved"===e.status&&(0,r.jsx)(a.N_,{href:`/tools/${e._id}`,className:"p-2 text-gray-400 hover:text-blue-600 transition-colors",title:c("tooltips.view_details"),children:(0,r.jsx)(g.A,{className:"h-5 w-5"})}),"draft"===e.status&&!e.launchDateSelected&&(0,r.jsx)(a.N_,{href:`/submit/launch-date/${e._id}`,className:"p-2 text-gray-400 hover:text-blue-600 transition-colors",title:c("tooltips.set_launch_date"),children:(0,r.jsx)(v.A,{className:"h-5 w-5"})}),["pending","approved"].includes(e.status)&&e.launchDateSelected&&(0,r.jsx)(a.N_,{href:`/submit/launch-date/${e._id}?mode=edit`,className:"p-2 text-gray-400 hover:text-orange-600 transition-colors",title:c("tooltips.modify_launch_date"),children:(0,r.jsx)(v.A,{className:"h-5 w-5"})}),(0,r.jsx)("a",{href:e.website,target:"_blank",rel:"noopener noreferrer",className:"p-2 text-gray-400 hover:text-green-600 transition-colors",title:c("tooltips.visit_website"),children:(0,r.jsx)(f.A,{className:"h-5 w-5"})}),["draft","pending","rejected","approved","published"].includes(e.status)&&(0,r.jsx)(a.N_,{href:`/submit/edit/${e._id}`,className:"p-2 text-gray-400 hover:text-blue-600 transition-colors",title:"approved"===e.status&&e.launchDate&&new Date(e.launchDate)<=new Date?c("tooltips.edit_basic_info"):"approved"===e.status?c("tooltips.edit_basic_info_no_url"):c("tooltips.edit_tool_info"),children:(0,r.jsx)(u.A,{className:"h-5 w-5"})})]})]})},e._id))}):(0,r.jsxs)("div",{className:"text-center py-12",children:[(0,r.jsx)("div",{className:"text-gray-400 mb-4",children:(0,r.jsx)(h.A,{className:"h-12 w-12 mx-auto"})}),(0,r.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"all"===m?c("empty_states.no_tools"):c("empty_states.no_status_tools",{status:S(m)})}),(0,r.jsx)("p",{className:"text-gray-600 mb-4",children:"all"===m?c("empty_states.get_started"):c("empty_states.try_other_status")}),"all"===m&&(0,r.jsxs)(a.N_,{href:"/submit",className:"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-lg text-white bg-blue-600 hover:bg-blue-700 transition-colors",children:[(0,r.jsx)(x.A,{className:"mr-2 h-4 w-4"}),c("submit_new_tool")]})]})})]})})}},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11011:(e,t,s)=>{"use strict";s.d(t,{default:()=>l});var r=s(60687),i=s(93613),a=s(11860);function l({message:e,onClose:t,className:s=""}){return(0,r.jsx)("div",{className:`bg-red-50 border border-red-200 rounded-lg p-4 ${s}`,children:(0,r.jsxs)("div",{className:"flex items-start",children:[(0,r.jsx)(i.A,{className:"w-5 h-5 text-red-500 mt-0.5 mr-3 flex-shrink-0"}),(0,r.jsx)("div",{className:"flex-1",children:(0,r.jsx)("p",{className:"text-red-800 text-sm",children:e})}),t&&(0,r.jsx)("button",{onClick:t,className:"ml-3 text-red-400 hover:text-red-600 transition-colors",children:(0,r.jsx)(a.A,{className:"w-4 h-4"})})]})})}},11723:e=>{"use strict";e.exports=require("querystring")},11860:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(62688).A)("x",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},12412:e=>{"use strict";e.exports=require("assert")},12909:(e,t,s)=>{"use strict";s.d(t,{N:()=>o});var r=s(36344),i=s(65752),a=s(13581),l=s(75745),n=s(17063);let o={...!1,providers:[(0,r.A)({clientId:process.env.GOOGLE_CLIENT_ID,clientSecret:process.env.GOOGLE_CLIENT_SECRET}),(0,i.A)({clientId:process.env.GITHUB_CLIENT_ID,clientSecret:process.env.GITHUB_CLIENT_SECRET}),(0,a.A)({id:"email-code",name:"Email Code",credentials:{email:{label:"Email",type:"email"},code:{label:"Code",type:"text"},token:{label:"Token",type:"text"}},async authorize(e){if(!e?.email||!e?.code||!e?.token)return null;try{await (0,l.A)();let t=await n.A.findOne({email:e.email.toLowerCase(),emailVerificationExpires:{$gt:new Date}});if(!t)return null;let s=t.emailVerificationToken;if(!s||!s.includes(":"))return null;let[r,i]=s.split(":");if(r!==e.token||i!==e.code)return null;return t.emailVerified=!0,t.emailVerificationToken=void 0,t.emailVerificationExpires=void 0,t.lastLoginAt=new Date,t.accounts.some(e=>"email"===e.provider)||t.accounts.push({provider:"email",providerId:"email",providerAccountId:t.email}),await t.save(),{id:t._id.toString(),email:t.email,name:t.name,image:t.avatar,role:t.role}}catch(e){return console.error("Email code authorization error:",e),null}}})],session:{strategy:"jwt"},callbacks:{async signIn({user:e,account:t,profile:s}){if(t?.provider==="email-code")return!0;await (0,l.A)();try{let r=await n.A.findOne({email:e.email});return r?r.lastLoginAt=new Date:r=new n.A({email:e.email,name:e.name||s?.name||"User",avatar:e.image||s?.image,emailVerified:!0,lastLoginAt:new Date}),await r.save(),t&&"email-code"!==t.provider&&(r.addAccount({provider:t.provider,providerId:t.provider,providerAccountId:t.providerAccountId||t.id||"",accessToken:t.access_token,refreshToken:t.refresh_token,expiresAt:t.expires_at?new Date(1e3*t.expires_at):void 0}),await r.save()),!0}catch(e){return console.error("Sign in error:",e),!1}},jwt:async({token:e,user:t})=>(t&&(e.userId=t.id,e.role=t.role||"user"),e),session:async({session:e,token:t})=>(t&&e.user&&(e.user.id=t.userId,e.user.role=t.role),e)},pages:{signIn:"/auth/signin",error:"/auth/error"},secret:process.env.NEXTAUTH_SECRET}},13861:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(62688).A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},17063:(e,t,s)=>{"use strict";s.d(t,{A:()=>n});var r=s(56037),i=s.n(r);let a=new r.Schema({provider:{type:String,required:!0,enum:["google","github","email"]},providerId:{type:String,required:!0},providerAccountId:{type:String,required:!0},accessToken:String,refreshToken:String,expiresAt:Date},{_id:!1}),l=new r.Schema({email:{type:String,required:[!0,"Email is required"],unique:!0,trim:!0,lowercase:!0,validate:{validator:function(e){return/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(e)},message:"Please enter a valid email address"}},name:{type:String,required:[!0,"Name is required"],trim:!0,maxlength:[100,"Name cannot exceed 100 characters"]},avatar:{type:String,trim:!0},role:{type:String,required:!0,enum:["user","admin"],default:"user"},isActive:{type:Boolean,default:!0},emailVerified:{type:Boolean,default:!1},emailVerificationToken:{type:String,trim:!0},emailVerificationExpires:{type:Date},accounts:[a],submittedTools:[{type:r.Schema.Types.ObjectId,ref:"Tool"}],likedTools:[{type:r.Schema.Types.ObjectId,ref:"Tool"}],comments:[{type:r.Schema.Types.ObjectId,ref:"Comment"}],lastLoginAt:{type:Date}},{timestamps:!0,toJSON:{virtuals:!0},toObject:{virtuals:!0}});l.index({email:1}),l.index({role:1}),l.index({emailVerificationToken:1}),l.index({"accounts.provider":1,"accounts.providerAccountId":1}),l.methods.addAccount=function(e){let t=this.accounts.find(t=>t.provider===e.provider&&t.providerAccountId===e.providerAccountId);t?Object.assign(t,e):this.accounts.push(e)},l.methods.removeAccount=function(e,t){this.accounts=this.accounts.filter(s=>s.provider!==e||s.providerAccountId!==t)};let n=i().models.User||i().model("User",l)},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},20856:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>l.a,__next_app__:()=>m,pages:()=>c,routeModule:()=>u,tree:()=>d});var r=s(65239),i=s(48088),a=s(88170),l=s.n(a),n=s(30893),o={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>n[e]);s.d(t,o);let d={children:["",{children:["[locale]",{children:["profile",{children:["submitted",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,52549)),"/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/profile/submitted/page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,32410)),"/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,94431)),"/Users/<USER>/workspace/aitools/aitools-website/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/profile/submitted/page.tsx"],m={require:s,loadChunk:()=>Promise.resolve()},u=new r.AppPageRouteModule({definition:{kind:i.RouteKind.APP_PAGE,page:"/[locale]/profile/submitted/page",pathname:"/[locale]/profile/submitted",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},25334:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(62688).A)("external-link",[["path",{d:"M15 3h6v6",key:"1q9fwt"}],["path",{d:"M10 14 21 3",key:"gplh6r"}],["path",{d:"M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6",key:"a6xqqp"}]])},28354:e=>{"use strict";e.exports=require("util")},28559:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(62688).A)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},30762:(e,t,s)=>{"use strict";s.d(t,{A:()=>n});var r=s(56037),i=s.n(r),a=s(60366);let l=new r.Schema({name:{type:String,required:[!0,"Tool name is required"],trim:!0,maxlength:[100,"Tool name cannot exceed 100 characters"]},tagline:{type:String,trim:!0,maxlength:[200,"Tagline cannot exceed 200 characters"]},description:{type:String,required:[!0,"Tool description is required"],trim:!0,maxlength:[500,"Description cannot exceed 500 characters"]},longDescription:{type:String,trim:!0,maxlength:[2e3,"Long description cannot exceed 2000 characters"]},website:{type:String,required:[!0,"Website URL is required"],trim:!0,validate:{validator:function(e){return/^https?:\/\/.+/.test(e)},message:"Please enter a valid URL"}},logo:{type:String,trim:!0},category:{type:String,required:[!0,"Category is required"],enum:a.ut},tags:[{type:String,trim:!0,lowercase:!0}],pricing:{type:String,required:[!0,"Pricing model is required"],enum:["free","freemium","paid"]},pricingDetails:{type:String,trim:!0,maxlength:[500,"Pricing details cannot exceed 500 characters"]},screenshots:[{type:String,trim:!0}],submittedBy:{type:String,required:[!0,"Submitter ID is required"],trim:!0},submittedAt:{type:Date,default:Date.now},launchDate:{type:Date},status:{type:String,required:!0,enum:["draft","pending","approved","rejected"],default:"draft"},reviewNotes:{type:String,trim:!0,maxlength:[1e3,"Review notes cannot exceed 1000 characters"]},reviewedBy:{type:String,trim:!0},reviewedAt:{type:Date},launchDateSelected:{type:Boolean,default:!1},selectedLaunchDate:{type:Date},launchOption:{type:String,enum:["free","paid"]},paymentRequired:{type:Boolean,default:!1},paymentAmount:{type:Number,min:0},paymentStatus:{type:String,enum:["pending","completed","failed","refunded"]},orderId:{type:String,trim:!0},paymentMethod:{type:String,trim:!0},paidAt:{type:Date},views:{type:Number,default:0,min:0},likes:{type:Number,default:0,min:0},likedBy:[{type:String,trim:!0}],isActive:{type:Boolean,default:!0}},{timestamps:!0,toJSON:{virtuals:!0},toObject:{virtuals:!0}});l.index({status:1,isActive:1}),l.index({category:1,status:1}),l.index({tags:1,status:1}),l.index({submittedBy:1}),l.index({launchDate:-1}),l.index({views:-1}),l.index({likes:-1}),l.index({name:"text",tagline:"text",description:"text",longDescription:"text",tags:"text"});let n=i().models.Tool||i().model("Tool",l)},31261:(e,t,s)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var s in t)Object.defineProperty(e,s,{enumerable:!0,get:t[s]})}(t,{default:function(){return o},getImageProps:function(){return n}});let r=s(37366),i=s(44953),a=s(46533),l=r._(s(1933));function n(e){let{props:t}=(0,i.getImgProps)(e,{defaultLoader:l.default,imgConf:{deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image",loader:"default",dangerouslyAllowSVG:!0,unoptimized:!1}});for(let[e,s]of Object.entries(t))void 0===s&&delete t[e];return{props:t}}let o=a.Image},33873:e=>{"use strict";e.exports=require("path")},35071:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(62688).A)("circle-x",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m15 9-6 6",key:"1uzhvr"}],["path",{d:"m9 9 6 6",key:"z0biqf"}]])},40228:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(62688).A)("calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])},48251:(e,t,s)=>{Promise.resolve().then(s.bind(s,75788)),Promise.resolve().then(s.bind(s,80994)),Promise.resolve().then(s.bind(s,2777))},48730:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(62688).A)("clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},52549:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>u});var r=s(37413),i=s(61120),a=s(35426),l=s(78878),n=s(12909),o=s(75745),d=s(30762),c=s(17063),m=s(2777);async function u({params:e}){let{locale:t}=await e,s=await (0,a.getServerSession)(n.N);s?.user?.email||(0,l.V2)({href:"/",locale:t}),await (0,o.A)();let u=await c.A.findOne({email:s.user.email});u||(0,l.V2)({href:"/",locale:t});let p=await d.A.find({submittedBy:u._id.toString()}).sort({submittedAt:-1}).lean(),x={total:p.length,draft:p.filter(e=>"draft"===e.status).length,approved:p.filter(e=>"approved"===e.status).length,pending:p.filter(e=>"pending"===e.status).length,rejected:p.filter(e=>"rejected"===e.status).length,totalViews:p.reduce((e,t)=>e+(t.views||0),0),totalLikes:p.reduce((e,t)=>e+(t.likes||0),0)},h=p.map(e=>({...e,_id:e._id?.toString()||"",submittedAt:e.submittedAt?.toISOString()||new Date().toISOString(),launchDate:e.launchDate?e.launchDate.toISOString():null,selectedLaunchDate:e.selectedLaunchDate?e.selectedLaunchDate.toISOString():null,reviewedAt:e.reviewedAt?e.reviewedAt.toISOString():null,paidAt:e.paidAt?e.paidAt.toISOString():null}));return(0,r.jsx)(i.Fragment,{children:(0,r.jsx)(m.default,{initialTools:h,initialStats:x})})}},53411:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(62688).A)("chart-column",[["path",{d:"M3 3v16a2 2 0 0 0 2 2h16",key:"c24i48"}],["path",{d:"M18 17V9",key:"2bz60n"}],["path",{d:"M13 17V5",key:"1frdt8"}],["path",{d:"M8 17v-3",key:"17ska0"}]])},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},56037:e=>{"use strict";e.exports=require("mongoose")},58867:(e,t,s)=>{Promise.resolve().then(s.bind(s,39130)),Promise.resolve().then(s.bind(s,45196)),Promise.resolve().then(s.bind(s,7211))},60366:(e,t,s)=>{"use strict";s.d(t,{BB:()=>l,PZ:()=>n,RI:()=>d,ut:()=>o});var r=s(64348);let i=[{slug:"text-generation",icon:"\uD83D\uDCDD",color:"#3B82F6"},{slug:"image-generation",icon:"\uD83C\uDFA8",color:"#10B981"},{slug:"code-generation",icon:"\uD83D\uDCBB",color:"#8B5CF6"},{slug:"data-analysis",icon:"\uD83D\uDCCA",color:"#F59E0B"},{slug:"audio-processing",icon:"\uD83C\uDFB5",color:"#EF4444"},{slug:"video-editing",icon:"\uD83C\uDFAC",color:"#06B6D4"},{slug:"translation",icon:"\uD83C\uDF10",color:"#84CC16"},{slug:"search-engines",icon:"\uD83D\uDD0D",color:"#F97316"},{slug:"education",icon:"\uD83D\uDCDA",color:"#A855F7"},{slug:"marketing",icon:"\uD83D\uDCC8",color:"#EC4899"},{slug:"productivity",icon:"⚡",color:"#14B8A6"},{slug:"customer-service",icon:"\uD83C\uDFA7",color:"#F59E0B"}];async function a(e){let t=await (0,r.A)({locale:e||"en",namespace:"categories"});return i.map(e=>({slug:e.slug,name:t(`category_names.${e.slug}`),description:t(`category_descriptions.${e.slug}`),icon:e.icon,color:e.color}))}async function l(e){return(await a(e)).map(e=>({value:e.slug,label:e.name}))}async function n(e,t){return(await a(t)).find(t=>t.slug===e)}let o=i.map(e=>e.slug),d=i.reduce((e,t)=>(e[t.slug]=t,e),{})},62688:(e,t,s)=>{"use strict";s.d(t,{A:()=>m});var r=s(43210);let i=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),a=e=>e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,t,s)=>s?s.toUpperCase():t.toLowerCase()),l=e=>{let t=a(e);return t.charAt(0).toUpperCase()+t.slice(1)},n=(...e)=>e.filter((e,t,s)=>!!e&&""!==e.trim()&&s.indexOf(e)===t).join(" ").trim(),o=e=>{for(let t in e)if(t.startsWith("aria-")||"role"===t||"title"===t)return!0};var d={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let c=(0,r.forwardRef)(({color:e="currentColor",size:t=24,strokeWidth:s=2,absoluteStrokeWidth:i,className:a="",children:l,iconNode:c,...m},u)=>(0,r.createElement)("svg",{ref:u,...d,width:t,height:t,stroke:e,strokeWidth:i?24*Number(s)/Number(t):s,className:n("lucide",a),...!l&&!o(m)&&{"aria-hidden":"true"},...m},[...c.map(([e,t])=>(0,r.createElement)(e,t)),...Array.isArray(l)?l:[l]])),m=(e,t)=>{let s=(0,r.forwardRef)(({className:s,...a},o)=>(0,r.createElement)(c,{ref:o,iconNode:t,className:n(`lucide-${i(l(e))}`,`lucide-${e}`,s),...a}));return s.displayName=l(e),s}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},63143:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(62688).A)("square-pen",[["path",{d:"M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1m0v6g"}],["path",{d:"M18.375 2.625a1 1 0 0 1 3 3l-9.013 9.014a2 2 0 0 1-.853.505l-2.873.84a.5.5 0 0 1-.62-.62l.84-2.873a2 2 0 0 1 .506-.852z",key:"ohrbg2"}]])},74075:e=>{"use strict";e.exports=require("zlib")},75745:(e,t,s)=>{"use strict";s.d(t,{A:()=>n});var r=s(56037),i=s.n(r);let a=process.env.MONGODB_URI;if(!a)throw Error("Please define the MONGODB_URI environment variable inside .env.local");let l=global.mongoose;l||(l=global.mongoose={conn:null,promise:null});let n=async function(){if(l.conn)return l.conn;l.promise||(l.promise=i().connect(a,{bufferCommands:!1}));try{l.conn=await l.promise}catch(e){throw l.promise=null,e}return l.conn}},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},93613:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(62688).A)("circle-alert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},94735:e=>{"use strict";e.exports=require("events")},96474:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(62688).A)("plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])}};var t=require("../../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[4243,4999,9658,6435,6699,8232,3136,6533,2585],()=>s(20856));module.exports=r})();