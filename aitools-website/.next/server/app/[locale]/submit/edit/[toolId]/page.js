"use strict";(()=>{var e={};e.id=3217,e.ids=[3217],e.modules={3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},8359:(e,r,t)=>{t.r(r),t.d(r,{default:()=>u});var o=t(37413),n=t(61120),i=t(60366),s=t(39636),a=t(10806),l=t(19854),c=t(12909),d=t(39916);async function u({params:e}){let{locale:r,toolId:t}=await e,u=await (0,l.getServerSession)(c.N);u?.user?.email||(0,d.redirect)(`/${r}/auth/signin?callbackUrl=/${r}/submit/edit/${t}`);let p=(await a.u.getTool(t))?.data;p||(0,d.redirect)(`/${r}/profile/submitted`);let m=await (0,i.BB)(r);return(0,o.jsx)(n.Fragment,{children:(0,o.jsx)(s.default,{categoryOptions:m,isEditMode:!0,toolId:t,initialTool:p})})}},10806:(e,r,t)=>{function o(){if(process.env.NEXT_PUBLIC_APP_URL)return process.env.NEXT_PUBLIC_APP_URL;{if(!process.env.VERCEL_URL&&!process.env.NETLIFY)return"http://localhost:3001";if(process.env.VERCEL_URL)return`https://${process.env.VERCEL_URL}`;if(process.env.NETLIFY&&process.env.URL)return process.env.URL;if(process.env.RAILWAY_STATIC_URL)return process.env.RAILWAY_STATIC_URL;if(process.env.APP_URL)return process.env.APP_URL;let e=process.env.PORT||"3001";return`http://localhost:${e}`}}function n(){if(process.env.NEXT_PUBLIC_API_BASE_URL)return process.env.NEXT_PUBLIC_API_BASE_URL;let e=o();return`${e}/api`}function i(){return"production"}function s(){return"development"===i()}t.d(r,{u:()=>d});let a={baseUrl:o(),apiBaseUrl:n(),nextAuthUrl:process.env.NEXTAUTH_URL?process.env.NEXTAUTH_URL:o(),environment:i(),isDevelopment:s(),isProduction:"production"===i(),port:process.env.PORT||"3001"};s()&&(console.log("\uD83D\uDD27 Dynamic Environment Configuration:"),console.log("  Base URL:",a.baseUrl),console.log("  API Base URL:",a.apiBaseUrl),console.log("  NextAuth URL:",a.nextAuthUrl),console.log("  Environment:",a.environment),console.log("  Port:",a.port));let l=n();class c{constructor(e=l){this.baseURL=e}async request(e,r={}){try{let t=`${this.baseURL}${e}`,o={headers:{"Content-Type":"application/json",...r.headers},...r},n=await fetch(t,o),i=await n.json();if(!n.ok)throw Error(i.error||`HTTP error! status: ${n.status}`);return i}catch(e){return console.error("API request failed:",e),{success:!1,error:e instanceof Error?e.message:"请求失败"}}}async getTools(e){let r=new URLSearchParams;e&&Object.entries(e).forEach(([e,t])=>{void 0!==t&&r.append(e,t.toString())});let t=r.toString();return this.request(`/tools${t?`?${t}`:""}`)}async getTool(e){return this.request(`/tools/${e}`)}async createTool(e){return this.request("/tools",{method:"POST",body:JSON.stringify(e)})}async updateTool(e,r){return this.request(`/tools/${e}`,{method:"PUT",body:JSON.stringify(r)})}async deleteTool(e){return this.request(`/tools/${e}`,{method:"DELETE"})}async getLikedTools(e){let r=new URLSearchParams;e&&Object.entries(e).forEach(([e,t])=>{void 0!==t&&r.append(e,t.toString())});let t=r.toString();return this.request(`/user/liked-tools${t?`?${t}`:""}`)}async getAdminTools(e){let r=new URLSearchParams;e&&Object.entries(e).forEach(([e,t])=>{void 0!==t&&r.append(e,t.toString())});let t=r.toString();return this.request(`/admin/tools${t?`?${t}`:""}`)}async approveTool(e,r){return this.request(`/admin/tools/${e}/approve`,{method:"POST",body:JSON.stringify(r)})}async rejectTool(e,r){return this.request(`/admin/tools/${e}/reject`,{method:"POST",body:JSON.stringify(r)})}async getAdminStats(e){let r=e?`?timeRange=${e}`:"";return this.request(`/admin/stats${r}`)}async getCategories(){return this.request("/categories")}async getOrder(e){return this.request(`/orders/${e}`)}async processOrderPayment(e,r){return this.request(`/orders/${e}/pay`,{method:"POST",body:JSON.stringify(r)})}}let d=new c},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11723:e=>{e.exports=require("querystring")},12269:(e,r)=>{Object.defineProperty(r,"__esModule",{value:!0})},12412:e=>{e.exports=require("assert")},12909:(e,r,t)=>{t.d(r,{N:()=>l});var o=t(36344),n=t(65752),i=t(13581),s=t(75745),a=t(17063);let l={...!1,providers:[(0,o.A)({clientId:process.env.GOOGLE_CLIENT_ID,clientSecret:process.env.GOOGLE_CLIENT_SECRET}),(0,n.A)({clientId:process.env.GITHUB_CLIENT_ID,clientSecret:process.env.GITHUB_CLIENT_SECRET}),(0,i.A)({id:"email-code",name:"Email Code",credentials:{email:{label:"Email",type:"email"},code:{label:"Code",type:"text"},token:{label:"Token",type:"text"}},async authorize(e){if(!e?.email||!e?.code||!e?.token)return null;try{await (0,s.A)();let r=await a.A.findOne({email:e.email.toLowerCase(),emailVerificationExpires:{$gt:new Date}});if(!r)return null;let t=r.emailVerificationToken;if(!t||!t.includes(":"))return null;let[o,n]=t.split(":");if(o!==e.token||n!==e.code)return null;return r.emailVerified=!0,r.emailVerificationToken=void 0,r.emailVerificationExpires=void 0,r.lastLoginAt=new Date,r.accounts.some(e=>"email"===e.provider)||r.accounts.push({provider:"email",providerId:"email",providerAccountId:r.email}),await r.save(),{id:r._id.toString(),email:r.email,name:r.name,image:r.avatar,role:r.role}}catch(e){return console.error("Email code authorization error:",e),null}}})],session:{strategy:"jwt"},callbacks:{async signIn({user:e,account:r,profile:t}){if(r?.provider==="email-code")return!0;await (0,s.A)();try{let o=await a.A.findOne({email:e.email});return o?o.lastLoginAt=new Date:o=new a.A({email:e.email,name:e.name||t?.name||"User",avatar:e.image||t?.image,emailVerified:!0,lastLoginAt:new Date}),await o.save(),r&&"email-code"!==r.provider&&(o.addAccount({provider:r.provider,providerId:r.provider,providerAccountId:r.providerAccountId||r.id||"",accessToken:r.access_token,refreshToken:r.refresh_token,expiresAt:r.expires_at?new Date(1e3*r.expires_at):void 0}),await o.save()),!0}catch(e){return console.error("Sign in error:",e),!1}},jwt:async({token:e,user:r})=>(r&&(e.userId=r.id,e.role=r.role||"user"),e),session:async({session:e,token:r})=>(r&&e.user&&(e.user.id=r.userId,e.user.role=r.role),e)},pages:{signIn:"/auth/signin",error:"/auth/error"},secret:process.env.NEXTAUTH_SECRET}},17063:(e,r,t)=>{t.d(r,{A:()=>a});var o=t(56037),n=t.n(o);let i=new o.Schema({provider:{type:String,required:!0,enum:["google","github","email"]},providerId:{type:String,required:!0},providerAccountId:{type:String,required:!0},accessToken:String,refreshToken:String,expiresAt:Date},{_id:!1}),s=new o.Schema({email:{type:String,required:[!0,"Email is required"],unique:!0,trim:!0,lowercase:!0,validate:{validator:function(e){return/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(e)},message:"Please enter a valid email address"}},name:{type:String,required:[!0,"Name is required"],trim:!0,maxlength:[100,"Name cannot exceed 100 characters"]},avatar:{type:String,trim:!0},role:{type:String,required:!0,enum:["user","admin"],default:"user"},isActive:{type:Boolean,default:!0},emailVerified:{type:Boolean,default:!1},emailVerificationToken:{type:String,trim:!0},emailVerificationExpires:{type:Date},accounts:[i],submittedTools:[{type:o.Schema.Types.ObjectId,ref:"Tool"}],likedTools:[{type:o.Schema.Types.ObjectId,ref:"Tool"}],comments:[{type:o.Schema.Types.ObjectId,ref:"Comment"}],lastLoginAt:{type:Date}},{timestamps:!0,toJSON:{virtuals:!0},toObject:{virtuals:!0}});s.index({email:1}),s.index({role:1}),s.index({emailVerificationToken:1}),s.index({"accounts.provider":1,"accounts.providerAccountId":1}),s.methods.addAccount=function(e){let r=this.accounts.find(r=>r.provider===e.provider&&r.providerAccountId===e.providerAccountId);r?Object.assign(r,e):this.accounts.push(e)},s.methods.removeAccount=function(e,r){this.accounts=this.accounts.filter(t=>t.provider!==e||t.providerAccountId!==r)};let a=n().models.User||n().model("User",s)},19121:e=>{e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19854:(e,r,t)=>{Object.defineProperty(r,"__esModule",{value:!0});var o={};Object.defineProperty(r,"default",{enumerable:!0,get:function(){return i.default}});var n=t(12269);Object.keys(n).forEach(function(e){!("default"===e||"__esModule"===e||Object.prototype.hasOwnProperty.call(o,e))&&(e in r&&r[e]===n[e]||Object.defineProperty(r,e,{enumerable:!0,get:function(){return n[e]}}))});var i=function(e,r){if(e&&e.__esModule)return e;if(null===e||"object"!=typeof e&&"function"!=typeof e)return{default:e};var t=s(r);if(t&&t.has(e))return t.get(e);var o={__proto__:null},n=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var i in e)if("default"!==i&&({}).hasOwnProperty.call(e,i)){var a=n?Object.getOwnPropertyDescriptor(e,i):null;a&&(a.get||a.set)?Object.defineProperty(o,i,a):o[i]=e[i]}return o.default=e,t&&t.set(e,o),o}(t(35426));function s(e){if("function"!=typeof WeakMap)return null;var r=new WeakMap,t=new WeakMap;return(s=function(e){return e?t:r})(e)}Object.keys(i).forEach(function(e){!("default"===e||"__esModule"===e||Object.prototype.hasOwnProperty.call(o,e))&&(e in r&&r[e]===i[e]||Object.defineProperty(r,e,{enumerable:!0,get:function(){return i[e]}}))})},28354:e=>{e.exports=require("util")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{e.exports=require("path")},55511:e=>{e.exports=require("crypto")},55591:e=>{e.exports=require("https")},56037:e=>{e.exports=require("mongoose")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},68458:(e,r,t)=>{t.r(r),t.d(r,{GlobalError:()=>s.a,__next_app__:()=>u,pages:()=>d,routeModule:()=>p,tree:()=>c});var o=t(65239),n=t(48088),i=t(88170),s=t.n(i),a=t(30893),l={};for(let e in a)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>a[e]);t.d(r,l);let c={children:["",{children:["[locale]",{children:["submit",{children:["edit",{children:["[toolId]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,8359)),"/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/submit/edit/[toolId]/page.tsx"]}]},{}]},{}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,32410)),"/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,94431)),"/Users/<USER>/workspace/aitools/aitools-website/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,d=["/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/submit/edit/[toolId]/page.tsx"],u={require:t,loadChunk:()=>Promise.resolve()},p=new o.AppPageRouteModule({definition:{kind:n.RouteKind.APP_PAGE,page:"/[locale]/submit/edit/[toolId]/page",pathname:"/[locale]/submit/edit/[toolId]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},74075:e=>{e.exports=require("zlib")},75745:(e,r,t)=>{t.d(r,{A:()=>a});var o=t(56037),n=t.n(o);let i=process.env.MONGODB_URI;if(!i)throw Error("Please define the MONGODB_URI environment variable inside .env.local");let s=global.mongoose;s||(s=global.mongoose={conn:null,promise:null});let a=async function(){if(s.conn)return s.conn;s.promise||(s.promise=n().connect(i,{bufferCommands:!1}));try{s.conn=await s.promise}catch(e){throw s.promise=null,e}return s.conn}},79428:e=>{e.exports=require("buffer")},79551:e=>{e.exports=require("url")},81630:e=>{e.exports=require("http")},94735:e=>{e.exports=require("events")}};var r=require("../../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),o=r.X(0,[4243,4999,9658,6435,6699,8232,3136,2585,1506],()=>t(68458));module.exports=o})();