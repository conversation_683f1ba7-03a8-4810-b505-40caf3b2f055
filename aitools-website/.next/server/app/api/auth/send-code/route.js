(()=>{var e={};e.id=6433,e.ids=[6433],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},7180:(e,t,i)=>{"use strict";let a=i(55511),s=i(29021),n=i(48178),o=i(27910).PassThrough,r=i(91292),p=i(18199),c=i(63356),l=i(89066),d=i(74508),m=i(38099),h=i(89047),u=i(39449),f=i(41748);class x{constructor(e,t){this.nodeCounter=0,t=t||{},this.baseBoundary=t.baseBoundary||a.randomBytes(8).toString("hex"),this.boundaryPrefix=t.boundaryPrefix||"--_NmP",this.disableFileAccess=!!t.disableFileAccess,this.disableUrlAccess=!!t.disableUrlAccess,this.normalizeHeaderKey=t.normalizeHeaderKey,this.date=new Date,this.rootNode=t.rootNode||this,this.keepBcc=!!t.keepBcc,t.filename&&(this.filename=t.filename,e||(e=p.detectMimeType(this.filename.split(".").pop()))),this.textEncoding=(t.textEncoding||"").toString().trim().charAt(0).toUpperCase(),this.parentNode=t.parentNode,this.hostname=t.hostname,this.newline=t.newline,this.childNodes=[],this._nodeId=++this.rootNode.nodeCounter,this._headers=[],this._isPlainText=!1,this._hasLongLines=!1,this._envelope=!1,this._raw=!1,this._transforms=[],this._processFuncs=[],e&&this.setHeader("Content-Type",e)}createChild(e,t){t||"object"!=typeof e||(t=e,e=void 0);let i=new x(e,t);return this.appendChild(i),i}appendChild(e){return e.rootNode!==this.rootNode&&(e.rootNode=this.rootNode,e._nodeId=++this.rootNode.nodeCounter),e.parentNode=this,this.childNodes.push(e),e}replace(e){return e===this?this:(this.parentNode.childNodes.forEach((t,i)=>{t===this&&(e.rootNode=this.rootNode,e.parentNode=this.parentNode,e._nodeId=this._nodeId,this.rootNode=this,this.parentNode=void 0,e.parentNode.childNodes[i]=e)}),e)}remove(){if(!this.parentNode)return this;for(let e=this.parentNode.childNodes.length-1;e>=0;e--)if(this.parentNode.childNodes[e]===this)return this.parentNode.childNodes.splice(e,1),this.parentNode=void 0,this.rootNode=this,this}setHeader(e,t){let i=!1,a;if(!t&&e&&"object"==typeof e)return e.key&&"value"in e?this.setHeader(e.key,e.value):Array.isArray(e)?e.forEach(e=>{this.setHeader(e.key,e.value)}):Object.keys(e).forEach(t=>{this.setHeader(t,e[t])}),this;a={key:e=this._normalizeHeaderKey(e),value:t};for(let t=0,s=this._headers.length;t<s;t++)this._headers[t].key===e&&(i?(this._headers.splice(t,1),t--,s--):(this._headers[t]=a,i=!0));return i||this._headers.push(a),this}addHeader(e,t){return!t&&e&&"object"==typeof e?e.key&&e.value?this.addHeader(e.key,e.value):Array.isArray(e)?e.forEach(e=>{this.addHeader(e.key,e.value)}):Object.keys(e).forEach(t=>{this.addHeader(t,e[t])}):Array.isArray(t)?t.forEach(t=>{this.addHeader(e,t)}):this._headers.push({key:this._normalizeHeaderKey(e),value:t}),this}getHeader(e){e=this._normalizeHeaderKey(e);for(let t=0,i=this._headers.length;t<i;t++)if(this._headers[t].key===e)return this._headers[t].value}setContent(e){return this.content=e,"function"==typeof this.content.pipe?(this._contentErrorHandler=e=>{this.content.removeListener("error",this._contentErrorHandler),this.content=e},this.content.once("error",this._contentErrorHandler)):"string"==typeof this.content&&(this._isPlainText=p.isPlainText(this.content),this._isPlainText&&p.hasLongerLines(this.content,76)&&(this._hasLongLines=!0)),this}build(e){let t;e||(t=new Promise((t,i)=>{e=r.callbackPromise(t,i)}));let i=this.createReadStream(),a=[],s=0,n=!1;return i.on("readable",()=>{let e;for(;null!==(e=i.read());)a.push(e),s+=e.length}),i.once("error",t=>{if(!n)return n=!0,e(t)}),i.once("end",t=>{if(!n)return n=!0,t&&t.length&&(a.push(t),s+=t.length),e(null,Buffer.concat(a,s))}),t}getTransferEncoding(){let e=!1,t=(this.getHeader("Content-Type")||"").toString().toLowerCase().trim();return this.content&&((e=(this.getHeader("Content-Transfer-Encoding")||"").toString().toLowerCase().trim())&&["base64","quoted-printable"].includes(e)||(/^text\//i.test(t)?e=this._isPlainText&&!this._hasLongLines?"7bit":"string"==typeof this.content||this.content instanceof Buffer?"Q"===this._getTextEncoding(this.content)?"quoted-printable":"base64":"B"===this.textEncoding?"base64":"quoted-printable":/^(multipart|message)\//i.test(t)||(e=e||"base64"))),e}buildHeaders(){let e=this.getTransferEncoding(),t=[];if(e&&this.setHeader("Content-Transfer-Encoding",e),this.filename&&!this.getHeader("Content-Disposition")&&this.setHeader("Content-Disposition","attachment"),this.rootNode===this){this.getHeader("Date")||this.setHeader("Date",this.date.toUTCString().replace(/GMT/,"+0000")),this.messageId(),this.getHeader("MIME-Version")||this.setHeader("MIME-Version","1.0");for(let e=this._headers.length-2;e>=0;e--){let t=this._headers[e];"Content-Type"===t.key&&(this._headers.splice(e,1),this._headers.push(t))}}return this._headers.forEach(e=>{let i,a,s=e.key,n=e.value,o={};if(!n||"object"!=typeof n||["From","Sender","To","Cc","Bcc","Reply-To","Date","References"].includes(s)||(Object.keys(n).forEach(e=>{"value"!==e&&(o[e]=n[e])}),(n=(n.value||"").toString()).trim())){if(o.prepared)return void(o.foldLines?t.push(p.foldLines(s+": "+n)):t.push(s+": "+n));switch(e.key){case"Content-Disposition":i=p.parseHeaderValue(n),this.filename&&(i.params.filename=this.filename),n=p.buildHeaderValue(i);break;case"Content-Type":i=p.parseHeaderValue(n),this._handleContentType(i),i.value.match(/^text\/plain\b/)&&"string"==typeof this.content&&/[\u0080-\uFFFF]/.test(this.content)&&(i.params.charset="utf-8"),n=p.buildHeaderValue(i),this.filename&&(((a=this._encodeWords(this.filename))!==this.filename||/[\s'"\\;:/=(),<>@[\]?]|^-/.test(a))&&(a='"'+a+'"'),n+="; name="+a);break;case"Bcc":if(!this.keepBcc)return}if(((n=this._encodeHeaderValue(s,n))||"").toString().trim()){if("function"==typeof this.normalizeHeaderKey){let e=this.normalizeHeaderKey(s,n);e&&"string"==typeof e&&e.length&&(s=e)}t.push(p.foldLines(s+": "+n,76))}}}),t.join("\r\n")}createReadStream(e){let t,i=new o(e=e||{}),a=i;this.stream(i,e,e=>{if(e)return void a.emit("error",e);i.end()});for(let e=0,i=this._transforms.length;e<i;e++)t="function"==typeof this._transforms[e]?this._transforms[e]():this._transforms[e],a.once("error",e=>{t.emit("error",e)}),a=a.pipe(t);t=new h,a.once("error",e=>{t.emit("error",e)}),a=a.pipe(t);for(let e=0,i=this._processFuncs.length;e<i;e++)a=(t=this._processFuncs[e])(a);if(this.newline){let e=["win","windows","dos","\r\n"].includes(this.newline.toString().toLowerCase())?new u:new f,t=a.pipe(e);return a.on("error",e=>t.emit("error",e)),t}return a}transform(e){this._transforms.push(e)}processFunc(e){this._processFuncs.push(e)}stream(e,t,i){let a,s,n=this.getTransferEncoding(),o=!1,r=e=>{o||(o=!0,i(e))},p=()=>{let i=0,a=()=>{if(i>=this.childNodes.length)return e.write("\r\n--"+this.boundary+"--\r\n"),r();let s=this.childNodes[i++];e.write((i>1?"\r\n":"")+"--"+this.boundary+"\r\n"),s.stream(e,t,e=>{if(e)return r(e);setImmediate(a)})};if(!this.multipart)return r();setImmediate(a)};this._raw?setImmediate(()=>{if("[object Error]"===Object.prototype.toString.call(this._raw))return r(this._raw);"function"==typeof this._raw.pipe&&this._raw.removeListener("error",this._contentErrorHandler);let t=this._getStream(this._raw);t.pipe(e,{end:!1}),t.on("error",t=>e.emit("error",t)),t.on("end",p)}):(e.write(this.buildHeaders()+"\r\n\r\n"),setImmediate(()=>{if(!this.content)return setImmediate(p);{if("[object Error]"===Object.prototype.toString.call(this.content))return r(this.content);"function"==typeof this.content.pipe&&(this.content.removeListener("error",this._contentErrorHandler),this._contentErrorHandler=e=>r(e),this.content.once("error",this._contentErrorHandler));let i=()=>{["quoted-printable","base64"].includes(n)?((a=new("base64"===n?l:c).Encoder(t)).pipe(e,{end:!1}),a.once("end",p),a.once("error",e=>r(e)),(s=this._getStream(this.content)).pipe(a)):((s=this._getStream(this.content)).pipe(e,{end:!1}),s.once("end",p)),s.once("error",e=>r(e))};if(this.content._resolve){let e=[],t=0,a=!1,s=this._getStream(this.content);s.on("error",e=>{a||(a=!0,r(e))}),s.on("readable",()=>{let i;for(;null!==(i=s.read());)e.push(i),t+=i.length}),s.on("end",()=>{a||(a=!0,this.content._resolve=!1,this.content._resolvedValue=Buffer.concat(e,t),setImmediate(i))})}else setImmediate(i);return}}))}setEnvelope(e){let t;this._envelope={from:!1,to:[]},e.from&&(t=[],this._convertAddresses(this._parseAddresses(e.from),t),(t=t.filter(e=>e&&e.address)).length&&t[0]&&(this._envelope.from=t[0].address)),["to","cc","bcc"].forEach(t=>{e[t]&&this._convertAddresses(this._parseAddresses(e[t]),this._envelope.to)}),this._envelope.to=this._envelope.to.map(e=>e.address).filter(e=>e);let i=["to","cc","bcc","from"];return Object.keys(e).forEach(t=>{i.includes(t)||(this._envelope[t]=e[t])}),this}getAddresses(){let e={};return this._headers.forEach(t=>{let i=t.key.toLowerCase();["from","sender","reply-to","to","cc","bcc"].includes(i)&&(Array.isArray(e[i])||(e[i]=[]),this._convertAddresses(this._parseAddresses(t.value),e[i]))}),e}getEnvelope(){if(this._envelope)return this._envelope;let e={from:!1,to:[]};return this._headers.forEach(t=>{let i=[];"From"===t.key||!e.from&&["Reply-To","Sender"].includes(t.key)?(this._convertAddresses(this._parseAddresses(t.value),i),i.length&&i[0]&&(e.from=i[0].address)):["To","Cc","Bcc"].includes(t.key)&&this._convertAddresses(this._parseAddresses(t.value),e.to)}),e.to=e.to.map(e=>e.address),e}messageId(){let e=this.getHeader("Message-ID");return e||(e=this._generateMessageId(),this.setHeader("Message-ID",e)),e}setRaw(e){return this._raw=e,this._raw&&"function"==typeof this._raw.pipe&&(this._contentErrorHandler=e=>{this._raw.removeListener("error",this._contentErrorHandler),this._raw=e},this._raw.once("error",this._contentErrorHandler)),this}_getStream(e){let t;return e._resolvedValue?(t=new o,setImmediate(()=>{try{t.end(e._resolvedValue)}catch(e){t.emit("error",e)}}),t):"function"==typeof e.pipe?e:e&&"string"==typeof e.path&&!e.href?this.disableFileAccess?(t=new o,setImmediate(()=>t.emit("error",Error("File access rejected for "+e.path))),t):s.createReadStream(e.path):e&&"string"==typeof e.href?this.disableUrlAccess?(t=new o,setImmediate(()=>t.emit("error",Error("Url access rejected for "+e.href))),t):m(e.href,{headers:e.httpHeaders}):(t=new o,setImmediate(()=>{try{t.end(e||"")}catch(e){t.emit("error",e)}}),t)}_parseAddresses(e){return[].concat.apply([],[].concat(e).map(e=>e&&e.address?(e.address=this._normalizeAddress(e.address),e.name=e.name||"",[e]):d(e)))}_normalizeHeaderKey(e){return e=(e||"").toString().replace(/\r?\n|\r/g," ").trim().toLowerCase().replace(/^X-SMTPAPI$|^(MIME|DKIM|ARC|BIMI)\b|^[a-z]|-(SPF|FBL|ID|MD5)$|-[a-z]/gi,e=>e.toUpperCase()).replace(/^Content-Features$/i,"Content-features")}_handleContentType(e){this.contentType=e.value.trim().toLowerCase(),this.multipart=!!/^multipart\//i.test(this.contentType)&&this.contentType.substr(this.contentType.indexOf("/")+1),this.multipart?this.boundary=e.params.boundary=e.params.boundary||this.boundary||this._generateBoundary():this.boundary=!1}_generateBoundary(){return this.rootNode.boundaryPrefix+"-"+this.rootNode.baseBoundary+"-Part_"+this._nodeId}_encodeHeaderValue(e,t){switch(e=this._normalizeHeaderKey(e)){case"From":case"Sender":case"To":case"Cc":case"Bcc":case"Reply-To":return this._convertAddresses(this._parseAddresses(t));case"Message-ID":case"In-Reply-To":case"Content-Id":return"<"!==(t=(t||"").toString().replace(/\r?\n|\r/g," ")).charAt(0)&&(t="<"+t),">"!==t.charAt(t.length-1)&&(t+=">"),t;case"References":return(t=[].concat.apply([],[].concat(t||"").map(e=>(e=(e||"").toString().replace(/\r?\n|\r/g," ").trim()).replace(/<[^>]*>/g,e=>e.replace(/\s/g,"")).split(/\s+/))).map(e=>("<"!==e.charAt(0)&&(e="<"+e),">"!==e.charAt(e.length-1)&&(e+=">"),e))).join(" ").trim();case"Date":if("[object Date]"===Object.prototype.toString.call(t))return t.toUTCString().replace(/GMT/,"+0000");return t=(t||"").toString().replace(/\r?\n|\r/g," "),this._encodeWords(t);case"Content-Type":case"Content-Disposition":return(t||"").toString().replace(/\r?\n|\r/g," ");default:return t=(t||"").toString().replace(/\r?\n|\r/g," "),this._encodeWords(t)}}_convertAddresses(e,t){let i=[];return t=t||[],[].concat(e||[]).forEach(e=>{if(e.address)e.address=this._normalizeAddress(e.address),e.name?e.name&&i.push(`${this._encodeAddressName(e.name)} <${e.address}>`):i.push(e.address.indexOf(" ")>=0?`<${e.address}>`:`${e.address}`),e.address&&!t.filter(t=>t.address===e.address).length&&t.push(e);else if(e.group){let a=(e.group.length?this._convertAddresses(e.group,t):"").trim();i.push(`${this._encodeAddressName(e.name)}:${a};`)}}),i.join(", ")}_normalizeAddress(e){let t,i=(e=(e||"").toString().replace(/[\x00-\x1F<>]+/g," ").trim()).lastIndexOf("@");if(i<0)return e;let a=e.substr(0,i),s=e.substr(i+1);try{t=n.toASCII(s.toLowerCase())}catch(e){}return a.indexOf(" ")>=0&&('"'!==a.charAt(0)&&(a='"'+a),'"'!==a.substr(-1)&&(a+='"')),`${a}@${t}`}_encodeAddressName(e){if(!/^[\w ]*$/.test(e))if(/^[\x20-\x7e]*$/.test(e))return'"'+e.replace(/([\\"])/g,"\\$1")+'"';else return p.encodeWord(e,this._getTextEncoding(e),52);return e}_encodeWords(e){return p.encodeWords(e,this._getTextEncoding(e),52,!0)}_getTextEncoding(e){e=(e||"").toString();let t=this.textEncoding;return t||(t=(e.match(/[\x00-\x08\x0B\x0C\x0E-\x1F\u0080-\uFFFF]/g)||[]).length<(e.match(/[a-z]/gi)||[]).length?"Q":"B"),t}_generateMessageId(){return"<"+[2,2,2,6].reduce((e,t)=>e+"-"+a.randomBytes(t).toString("hex"),a.randomBytes(4).toString("hex"))+"@"+(this.getEnvelope().from||this.hostname||"localhost").split("@").pop()+">"}}e.exports=x},7575:(e,t,i)=>{"use strict";let a=i(91645),s=i(34631),n=i(79551);e.exports=function e(t,i,o,r){let p,c,l,d=n.parse(t);p={host:d.hostname,port:Number(d.port)?Number(d.port):"https:"===d.protocol?443:80},"https:"===d.protocol?(p.rejectUnauthorized=!1,c=s.connect.bind(s)):c=a.connect.bind(a);let m=!1,h=e=>{if(!m){m=!0;try{l.destroy()}catch(e){}r(e)}},u=()=>{let e=Error("Proxy socket timed out");e.code="ETIMEDOUT",h(e)};(l=c(p,()=>{if(m)return;let e={Host:o+":"+i,Connection:"close"};d.auth&&(e["Proxy-Authorization"]="Basic "+Buffer.from(d.auth).toString("base64")),l.write("CONNECT "+o+":"+i+" HTTP/1.1\r\n"+Object.keys(e).map(t=>t+": "+e[t]).join("\r\n")+"\r\n\r\n");let t="",a=e=>{let i,s;if(!m&&(t+=e.toString("binary"),i=t.match(/\r\n\r\n/))){if(l.removeListener("data",a),s=t.substr(i.index+i[0].length),t=t.substr(0,i.index),s&&l.unshift(Buffer.from(s,"binary")),m=!0,!(i=t.match(/^HTTP\/\d+\.\d+ (\d+)/i))||"2"!==(i[1]||"").charAt(0)){try{l.destroy()}catch(e){}return r(Error("Invalid response from proxy"+(i&&": "+i[1]||"")))}return l.removeListener("error",h),l.removeListener("timeout",u),l.setTimeout(0),r(null,l)}};l.on("data",a)})).setTimeout(e.timeout||3e4),l.on("timeout",u),l.once("error",h)}},8346:(e,t,i)=>{"use strict";let a=i(94735),s=i(89231),n=i(20068),o=i(16435),r=i(91292),p=i(49074);class c extends a{constructor(e){let t;super(),"string"==typeof(e=e||{})&&(e={url:e});let i=e.service;"function"==typeof e.getSocket&&(this.getSocket=e.getSocket),e.url&&(t=r.parseConnectionUrl(e.url),i=i||t.service),this.options=r.assign(!1,e,t,i&&o(i)),this.options.maxConnections=this.options.maxConnections||5,this.options.maxMessages=this.options.maxMessages||100,this.logger=r.getLogger(this.options,{component:this.options.component||"smtp-pool"});let a=new n(this.options);this.name="SMTP (pool)",this.version=p.version+"[client:"+a.version+"]",this._rateLimit={counter:0,timeout:null,waiting:[],checkpoint:!1,delta:Number(this.options.rateDelta)||1e3,limit:Number(this.options.rateLimit)||0},this._closed=!1,this._queue=[],this._connections=[],this._connectionCounter=0,this.idling=!0,setImmediate(()=>{this.idling&&this.emit("idle")})}getSocket(e,t){return setImmediate(()=>t(null,!1))}send(e,t){return!this._closed&&(this._queue.push({mail:e,requeueAttempts:0,callback:t}),this.idling&&this._queue.length>=this.options.maxConnections&&(this.idling=!1),setImmediate(()=>this._processMessages()),!0)}close(){let e,t=this._connections.length;if(this._closed=!0,clearTimeout(this._rateLimit.timeout),!t&&!this._queue.length)return;for(let i=t-1;i>=0;i--)this._connections[i]&&this._connections[i].available&&((e=this._connections[i]).close(),this.logger.info({tnx:"connection",cid:e.id,action:"removed"},"Connection #%s removed",e.id));if(t&&!this._connections.length&&this.logger.debug({tnx:"connection"},"All connections removed"),!this._queue.length)return;let i=()=>{if(!this._queue.length)return void this.logger.debug({tnx:"connection"},"Pending queue entries cleared");let t=this._queue.shift();if(t&&"function"==typeof t.callback)try{t.callback(Error("Connection pool was closed"))}catch(t){this.logger.error({err:t,tnx:"callback",cid:e.id},"Callback error for #%s: %s",e.id,t.message)}setImmediate(i)};setImmediate(i)}_processMessages(){let e,t,i;if(this._closed)return;if(!this._queue.length){this.idling||(this.idling=!0,this.emit("idle"));return}for(t=0,i=this._connections.length;t<i;t++)if(this._connections[t].available){e=this._connections[t];break}if(!e&&this._connections.length<this.options.maxConnections&&(e=this._createConnection()),!e){this.idling=!1;return}!this.idling&&this._queue.length<this.options.maxConnections&&(this.idling=!0,this.emit("idle"));let a=e.queueEntry=this._queue.shift();a.messageId=(e.queueEntry.mail.message.getHeader("message-id")||"").replace(/[<>\s]/g,""),e.available=!1,this.logger.debug({tnx:"pool",cid:e.id,messageId:a.messageId,action:"assign"},"Assigned message <%s> to #%s (%s)",a.messageId,e.id,e.messages+1),this._rateLimit.limit&&(this._rateLimit.counter++,this._rateLimit.checkpoint||(this._rateLimit.checkpoint=Date.now())),e.send(a.mail,(t,i)=>{if(a===e.queueEntry){try{a.callback(t,i)}catch(t){this.logger.error({err:t,tnx:"callback",cid:e.id},"Callback error for #%s: %s",e.id,t.message)}e.queueEntry=!1}})}_createConnection(){let e=new s(this);return e.id=++this._connectionCounter,this.logger.info({tnx:"pool",cid:e.id,action:"conection"},"Created new pool resource #%s",e.id),e.on("available",()=>{this.logger.debug({tnx:"connection",cid:e.id,action:"available"},"Connection #%s became available",e.id),this._closed?this.close():this._processMessages()}),e.once("error",t=>{if("EMAXLIMIT"!==t.code?this.logger.error({err:t,tnx:"pool",cid:e.id},"Pool Error for #%s: %s",e.id,t.message):this.logger.debug({tnx:"pool",cid:e.id,action:"maxlimit"},"Max messages limit exchausted for #%s",e.id),e.queueEntry){try{e.queueEntry.callback(t)}catch(t){this.logger.error({err:t,tnx:"callback",cid:e.id},"Callback error for #%s: %s",e.id,t.message)}e.queueEntry=!1}this._removeConnection(e),this._continueProcessing()}),e.once("close",()=>{this.logger.info({tnx:"connection",cid:e.id,action:"closed"},"Connection #%s was closed",e.id),this._removeConnection(e),e.queueEntry?setTimeout(()=>{e.queueEntry&&(this._shouldRequeuOnConnectionClose(e.queueEntry)?this._requeueEntryOnConnectionClose(e):this._failDeliveryOnConnectionClose(e)),this._continueProcessing()},50):this._continueProcessing()}),this._connections.push(e),e}_shouldRequeuOnConnectionClose(e){return void 0===this.options.maxRequeues||this.options.maxRequeues<0||e.requeueAttempts<this.options.maxRequeues}_failDeliveryOnConnectionClose(e){if(e.queueEntry&&e.queueEntry.callback){try{e.queueEntry.callback(Error("Reached maximum number of retries after connection was closed"))}catch(t){this.logger.error({err:t,tnx:"callback",messageId:e.queueEntry.messageId,cid:e.id},"Callback error for #%s: %s",e.id,t.message)}e.queueEntry=!1}}_requeueEntryOnConnectionClose(e){e.queueEntry.requeueAttempts=e.queueEntry.requeueAttempts+1,this.logger.debug({tnx:"pool",cid:e.id,messageId:e.queueEntry.messageId,action:"requeue"},"Re-queued message <%s> for #%s. Attempt: #%s",e.queueEntry.messageId,e.id,e.queueEntry.requeueAttempts),this._queue.unshift(e.queueEntry),e.queueEntry=!1}_continueProcessing(){this._closed?this.close():setTimeout(()=>this._processMessages(),100)}_removeConnection(e){let t=this._connections.indexOf(e);-1!==t&&this._connections.splice(t,1)}_checkRateLimit(e){if(!this._rateLimit.limit)return e();let t=Date.now();return this._rateLimit.counter<this._rateLimit.limit?e():(this._rateLimit.waiting.push(e),this._rateLimit.checkpoint<=t-this._rateLimit.delta)?this._clearRateLimit():void(!this._rateLimit.timeout&&(this._rateLimit.timeout=setTimeout(()=>this._clearRateLimit(),this._rateLimit.delta-(t-this._rateLimit.checkpoint)),this._rateLimit.checkpoint=t))}_clearRateLimit(){for(clearTimeout(this._rateLimit.timeout),this._rateLimit.timeout=null,this._rateLimit.counter=0,this._rateLimit.checkpoint=!1;this._rateLimit.waiting.length;)setImmediate(this._rateLimit.waiting.shift())}isIdle(){return this.idling}verify(e){let t;e||(t=new Promise((t,i)=>{e=r.callbackPromise(t,i)}));let i=new s(this).auth;return this.getSocket(this.options,(t,a)=>{if(t)return e(t);let s=this.options;a&&a.connection&&(this.logger.info({tnx:"proxy",remoteAddress:a.connection.remoteAddress,remotePort:a.connection.remotePort,destHost:s.host||"",destPort:s.port||"",action:"connected"},"Using proxied socket from %s:%s to %s:%s",a.connection.remoteAddress,a.connection.remotePort,s.host||"",s.port||""),s=r.assign(!1,s),Object.keys(a).forEach(e=>{s[e]=a[e]}));let o=new n(s),p=!1;o.once("error",t=>{if(!p)return p=!0,o.close(),e(t)}),o.once("end",()=>{if(!p)return p=!0,e(Error("Connection closed"))});let c=()=>{if(!p)return p=!0,o.quit(),e(null,!0)};o.connect(()=>{if(!p)if(i&&(o.allowsAuth||s.forceAuth))o.login(i,t=>{if(!p){if(t)return p=!0,o.close(),e(t);c()}});else if(!i&&o.allowsAuth&&s.forceAuth){let t=Error("Authentication info was not provided");return t.code="NoAuth",p=!0,o.close(),e(t)}else c()})}),t}}e.exports=c},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},14985:e=>{"use strict";e.exports=require("dns")},16435:(e,t,i)=>{"use strict";let a=i(58417),s={};function n(e){return e.replace(/[^a-zA-Z0-9.-]/g,"").toLowerCase()}function o(e){let t=["domains","aliases"],i={};return Object.keys(e).forEach(a=>{0>t.indexOf(a)&&(i[a]=e[a])}),i}Object.keys(a).forEach(e=>{let t=a[e];s[n(e)]=o(t),[].concat(t.aliases||[]).forEach(e=>{s[n(e)]=o(t)}),[].concat(t.domains||[]).forEach(e=>{s[n(e)]=o(t)})}),e.exports=function(e){return s[e=n(e.split("@").pop())]||!1}},17063:(e,t,i)=>{"use strict";i.d(t,{A:()=>r});var a=i(56037),s=i.n(a);let n=new a.Schema({provider:{type:String,required:!0,enum:["google","github","email"]},providerId:{type:String,required:!0},providerAccountId:{type:String,required:!0},accessToken:String,refreshToken:String,expiresAt:Date},{_id:!1}),o=new a.Schema({email:{type:String,required:[!0,"Email is required"],unique:!0,trim:!0,lowercase:!0,validate:{validator:function(e){return/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(e)},message:"Please enter a valid email address"}},name:{type:String,required:[!0,"Name is required"],trim:!0,maxlength:[100,"Name cannot exceed 100 characters"]},avatar:{type:String,trim:!0},role:{type:String,required:!0,enum:["user","admin"],default:"user"},isActive:{type:Boolean,default:!0},emailVerified:{type:Boolean,default:!1},emailVerificationToken:{type:String,trim:!0},emailVerificationExpires:{type:Date},accounts:[n],submittedTools:[{type:a.Schema.Types.ObjectId,ref:"Tool"}],likedTools:[{type:a.Schema.Types.ObjectId,ref:"Tool"}],comments:[{type:a.Schema.Types.ObjectId,ref:"Comment"}],lastLoginAt:{type:Date}},{timestamps:!0,toJSON:{virtuals:!0},toObject:{virtuals:!0}});o.index({email:1}),o.index({role:1}),o.index({emailVerificationToken:1}),o.index({"accounts.provider":1,"accounts.providerAccountId":1}),o.methods.addAccount=function(e){let t=this.accounts.find(t=>t.provider===e.provider&&t.providerAccountId===e.providerAccountId);t?Object.assign(t,e):this.accounts.push(e)},o.methods.removeAccount=function(e,t){this.accounts=this.accounts.filter(i=>i.provider!==e||i.providerAccountId!==t)};let r=s().models.User||s().model("User",o)},17704:(e,t,i)=>{"use strict";let a=i(91292),s=i(7180),n=i(18199);class o{constructor(e,t){this.mailer=e,this.data={},this.message=null,t=t||{};let i=e.options||{},a=e._defaults||{};Object.keys(t).forEach(e=>{this.data[e]=t[e]}),this.data.headers=this.data.headers||{},Object.keys(a).forEach(e=>{e in this.data?"headers"===e&&Object.keys(a.headers).forEach(e=>{e in this.data.headers||(this.data.headers[e]=a.headers[e])}):this.data[e]=a[e]}),["disableFileAccess","disableUrlAccess","normalizeHeaderKey"].forEach(e=>{e in i&&(this.data[e]=i[e])})}resolveContent(...e){return a.resolveContent(...e)}resolveAll(e){let t=[[this.data,"html"],[this.data,"text"],[this.data,"watchHtml"],[this.data,"amp"],[this.data,"icalEvent"]];this.data.alternatives&&this.data.alternatives.length&&this.data.alternatives.forEach((e,i)=>{t.push([this.data.alternatives,i])}),this.data.attachments&&this.data.attachments.length&&this.data.attachments.forEach((e,i)=>{!e.filename&&(e.filename=(e.path||e.href||"").split("/").pop().split("?").shift()||"attachment-"+(i+1),0>e.filename.indexOf(".")&&(e.filename+="."+n.detectExtension(e.contentType))),e.contentType||(e.contentType=n.detectMimeType(e.filename||e.path||e.href||"bin")),t.push([this.data.attachments,i])});let i=new s;["from","to","cc","bcc","sender","replyTo"].forEach(e=>{let t;this.message?t=[].concat(i._parseAddresses(this.message.getHeader("replyTo"===e?"reply-to":e))||[]):this.data[e]&&(t=[].concat(i._parseAddresses(this.data[e])||[])),t&&t.length?this.data[e]=t:e in this.data&&(this.data[e]=null)}),["from","sender"].forEach(e=>{this.data[e]&&(this.data[e]=this.data[e].shift())});let o=0,r=()=>{if(o>=t.length)return e(null,this.data);let i=t[o++];if(!i[0]||!i[0][i[1]])return r();a.resolveContent(...i,(t,a)=>{if(t)return e(t);let s={content:a};i[0][i[1]]&&"object"==typeof i[0][i[1]]&&!Buffer.isBuffer(i[0][i[1]])&&Object.keys(i[0][i[1]]).forEach(e=>{e in s||["content","path","href","raw"].includes(e)||(s[e]=i[0][i[1]][e])}),i[0][i[1]]=s,r()})};setImmediate(()=>r())}normalize(e){let t=this.data.envelope||this.message.getEnvelope(),i=this.message.messageId();this.resolveAll((a,s)=>a?e(a):(s.envelope=t,s.messageId=i,["html","text","watchHtml","amp"].forEach(e=>{s[e]&&s[e].content&&("string"==typeof s[e].content?s[e]=s[e].content:Buffer.isBuffer(s[e].content)&&(s[e]=s[e].content.toString()))}),s.icalEvent&&Buffer.isBuffer(s.icalEvent.content)&&(s.icalEvent.content=s.icalEvent.content.toString("base64"),s.icalEvent.encoding="base64"),s.alternatives&&s.alternatives.length&&s.alternatives.forEach(e=>{e&&e.content&&Buffer.isBuffer(e.content)&&(e.content=e.content.toString("base64"),e.encoding="base64")}),s.attachments&&s.attachments.length&&s.attachments.forEach(e=>{e&&e.content&&Buffer.isBuffer(e.content)&&(e.content=e.content.toString("base64"),e.encoding="base64")}),s.normalizedHeaders={},Object.keys(s.headers||{}).forEach(e=>{let t=[].concat(s.headers[e]||[]).shift();(t=t&&t.value||t)&&(["references","in-reply-to","message-id","content-id"].includes(e)&&(t=this.message._encodeHeaderValue(e,t)),s.normalizedHeaders[e]=t)}),s.list&&"object"==typeof s.list&&this._getListHeaders(s.list).forEach(e=>{s.normalizedHeaders[e.key]=e.value.map(e=>e&&e.value||e).join(", ")}),s.references&&(s.normalizedHeaders.references=this.message._encodeHeaderValue("references",s.references)),s.inReplyTo&&(s.normalizedHeaders["in-reply-to"]=this.message._encodeHeaderValue("in-reply-to",s.inReplyTo)),e(null,s)))}setMailerHeader(){this.message&&this.data.xMailer&&this.message.setHeader("X-Mailer",this.data.xMailer)}setPriorityHeaders(){if(this.message&&this.data.priority)switch((this.data.priority||"").toString().toLowerCase()){case"high":this.message.setHeader("X-Priority","1 (Highest)"),this.message.setHeader("X-MSMail-Priority","High"),this.message.setHeader("Importance","High");break;case"low":this.message.setHeader("X-Priority","5 (Lowest)"),this.message.setHeader("X-MSMail-Priority","Low"),this.message.setHeader("Importance","Low")}}setListHeaders(){this.message&&this.data.list&&"object"==typeof this.data.list&&this.data.list&&"object"==typeof this.data.list&&this._getListHeaders(this.data.list).forEach(e=>{e.value.forEach(t=>{this.message.addHeader(e.key,t)})})}_getListHeaders(e){return Object.keys(e).map(t=>({key:"list-"+t.toLowerCase().trim(),value:[].concat(e[t]||[]).map(e=>({prepared:!0,foldLines:!0,value:[].concat(e||[]).map(e=>{if("string"==typeof e&&(e={url:e}),e&&e.url){if("id"===t.toLowerCase().trim()){let t=e.comment||"";return t=n.isPlainText(t)?'"'+t+'"':n.encodeWord(t),(e.comment?t+" ":"")+this._formatListUrl(e.url).replace(/^<[^:]+\/{,2}/,"")}let i=e.comment||"";return n.isPlainText(i)||(i=n.encodeWord(i)),this._formatListUrl(e.url)+(e.comment?" ("+i+")":"")}return""}).filter(e=>e).join(", ")}))}))}_formatListUrl(e){return(e=e.replace(/[\s<]+|[\s>]+/g,""),/^(https?|mailto|ftp):/.test(e))?"<"+e+">":/^[^@]+@[^@]+$/.test(e)?"<mailto:"+e+">":"<http://"+e+">"}}e.exports=o},18199:(e,t,i)=>{"use strict";let a=i(89066),s=i(63356),n=i(50143);e.exports={isPlainText:(e,t)=>!("string"!=typeof e||(t?/[\x00-\x08\x0b\x0c\x0e-\x1f"\u0080-\uFFFF]/:/[\x00-\x08\x0b\x0c\x0e-\x1f\u0080-\uFFFF]/).test(e)),hasLongerLines:(e,t)=>e.length>131072||RegExp("^.{"+(t+1)+",}","m").test(e),encodeWord(e,t,i){let n;t=(t||"Q").toString().toUpperCase().trim().charAt(0);let o="UTF-8";if((i=i||0)&&i>7+o.length&&(i-=7+o.length),"Q"===t?n=s.encode(e).replace(/[^a-z0-9!*+\-/=]/gi,e=>{let t=e.charCodeAt(0).toString(16).toUpperCase();return" "===e?"_":"="+(1===t.length?"0"+t:t)}):"B"===t&&(n="string"==typeof e?e:a.encode(e),i=i?Math.max(3,(i-i%4)/4*3):0),i&&("B"!==t?n:a.encode(e)).length>i)if("Q"===t)n=this.splitMimeEncodedString(n,i).join("?= =?"+o+"?"+t+"?");else{let e=[],s="";for(let t=0,o=n.length;t<o;t++){let r=n.charAt(t);/[\ud83c\ud83d\ud83e]/.test(r)&&t<o-1&&(r+=n.charAt(++t)),Buffer.byteLength(s+r)<=i||0===t?s+=r:(e.push(a.encode(s)),s=r)}s&&e.push(a.encode(s)),n=e.length>1?e.join("?= =?"+o+"?"+t+"?"):e.join("")}else"B"===t&&(n=a.encode(e));return"=?"+o+"?"+t+"?"+n+("?="===n.substr(-2)?"":"?=")},encodeWords(e,t,i,a){i=i||0;let s=e.match(/(?:^|\s)([^\s]*["\u0080-\uFFFF])/);if(!s)return e;if(a)return this.encodeWord(e,t,i);let n=e.match(/(["\u0080-\uFFFF][^\s]*)[^"\u0080-\uFFFF]*$/);if(!n)return e;let o=s.index+(s[0].match(/[^\s]/)||{index:0}).index,r=n.index+(n[1]||"").length;return(o?e.substr(0,o):"")+this.encodeWord(e.substring(o,r),t||"Q",i)+(r<e.length?e.substr(r):"")},buildHeaderValue(e){let t=[];return Object.keys(e.params||{}).forEach(i=>{let a=e.params[i];!this.isPlainText(a,!0)||a.length>=75?this.buildHeaderParam(i,a,50).forEach(e=>{/[\s"\\;:/=(),<>@[\]?]|^[-']|'$/.test(e.value)&&"*"!==e.key.substr(-1)?t.push(e.key+"="+JSON.stringify(e.value)):t.push(e.key+"="+e.value)}):/[\s'"\\;:/=(),<>@[\]?]|^-/.test(a)?t.push(i+"="+JSON.stringify(a)):t.push(i+"="+a)}),e.value+(t.length?"; "+t.join("; "):"")},buildHeaderParam(e,t,i){let a,s,n,o,r,p,c=[],l="string"==typeof t?t:(t||"").toString(),d=0;if(i=i||50,this.isPlainText(t,!0)){if(l.length<=i)return[{key:e,value:l}];(l=l.replace(RegExp(".{"+i+"}","g"),e=>(c.push({line:e}),"")))&&c.push({line:l})}else{if(/[\uD800-\uDBFF]/.test(l)){for(r=0,a=[],p=l.length;r<p;r++)(n=(s=l.charAt(r)).charCodeAt(0))>=55296&&n<=56319&&r<p-1?(s+=l.charAt(r+1),a.push(s),r++):a.push(s);l=a}o="utf-8''";let e=!0;for(r=0,d=0,p=l.length;r<p;r++){if(s=l[r],e)s=this.safeEncodeURIComponent(s);else if((s=" "===s?s:this.safeEncodeURIComponent(s))!==l[r])if((this.safeEncodeURIComponent(o)+s).length>=i)c.push({line:o,encoded:e}),o="",d=r-1;else{e=!0,r=d,o="";continue}(o+s).length>=i?(c.push({line:o,encoded:e}),o=s=" "===l[r]?" ":this.safeEncodeURIComponent(l[r]),s===l[r]?(e=!1,d=r-1):e=!0):o+=s}o&&c.push({line:o,encoded:e})}return c.map((t,i)=>({key:e+"*"+i+(t.encoded?"*":""),value:t.line}))},parseHeaderValue(e){let t,i={value:!1,params:{}},a=!1,s="",n="value",o=!1,r=!1;for(let p=0,c=e.length;p<c;p++)if(t=e.charAt(p),"key"===n){if("="===t){a=s.trim().toLowerCase(),n="value",s="";continue}s+=t}else{if(r)s+=t;else if("\\"===t){r=!0;continue}else o&&t===o?o=!1:o||'"'!==t?o||";"!==t?s+=t:(!1===a?i.value=s.trim():i.params[a]=s.trim(),n="key",s=""):o=t;r=!1}return"value"===n?!1===a?i.value=s.trim():i.params[a]=s.trim():s.trim()&&(i.params[s.trim().toLowerCase()]=""),Object.keys(i.params).forEach(e=>{let t,a,s,n;(s=e.match(/(\*(\d+)|\*(\d+)\*|\*)$/))&&(t=e.substr(0,s.index),a=Number(s[2]||s[3])||0,i.params[t]&&"object"==typeof i.params[t]||(i.params[t]={charset:!1,values:[]}),n=i.params[e],0===a&&"*"===s[0].substr(-1)&&(s=n.match(/^([^']*)'[^']*'(.*)$/))&&(i.params[t].charset=s[1]||"iso-8859-1",n=s[2]),i.params[t].values[a]=n,delete i.params[e])}),Object.keys(i.params).forEach(e=>{let t;i.params[e]&&Array.isArray(i.params[e].values)&&(t=i.params[e].values.map(e=>e||"").join(""),i.params[e].charset?i.params[e]="=?"+i.params[e].charset+"?Q?"+t.replace(/[=?_\s]/g,e=>{let t=e.charCodeAt(0).toString(16);return" "===e?"_":"%"+(t.length<2?"0":"")+t}).replace(/%/g,"=")+"?=":i.params[e]=t)}),i},detectExtension:e=>n.detectExtension(e),detectMimeType:e=>n.detectMimeType(e),foldLines(e,t,i){e=(e||"").toString(),t=t||76;let a=0,s=e.length,n="",o,r;for(;a<s;){if((o=e.substr(a,t)).length<t){n+=o;break}if(r=o.match(/^[^\n\r]*(\r?\n|\r)/)){n+=o=r[0],a+=o.length;continue}(r=o.match(/(\s+)[^\s]*$/))&&r[0].length-(i?(r[1]||"").length:0)<o.length?o=o.substr(0,o.length-(r[0].length-(i?(r[1]||"").length:0))):(r=e.substr(a+o.length).match(/^[^\s]+(\s*)/))&&(o+=r[0].substr(0,r[0].length-(i?0:(r[1]||"").length))),n+=o,(a+=o.length)<s&&(n+="\r\n")}return n},splitMimeEncodedString:(e,t)=>{let i,a,s,n,o=[];for(t=Math.max(t||0,12);e.length;){for((a=(i=e.substr(0,t)).match(/[=][0-9A-F]?$/i))&&(i=i.substr(0,a.index)),n=!1;!n;)n=!0,(a=e.substr(i.length).match(/^[=]([0-9A-F]{2})/i))&&(s=parseInt(a[1],16))<194&&s>127&&(i=i.substr(0,i.length-3),n=!1);i.length&&o.push(i),e=e.substr(i.length)}return o},encodeURICharComponent:e=>{let t="",i=e.charCodeAt(0).toString(16).toUpperCase();if(i.length%2&&(i="0"+i),i.length>2)for(let e=0,a=i.length/2;e<a;e++)t+="%"+i.substr(e,2);else t+="%"+i;return t},safeEncodeURIComponent(e){e=(e||"").toString();try{e=encodeURIComponent(e)}catch(t){return e.replace(/[^\x00-\x1F *'()<>@,;:\\"[\]?=\u007F-\uFFFF]+/g,"")}return e.replace(/[\x00-\x1F *'()<>@,;:\\"[\]?=\u007F-\uFFFF]/g,e=>this.encodeURICharComponent(e))}}},20068:(e,t,i)=>{"use strict";let a=i(49074),s=i(94735).EventEmitter,n=i(91645),o=i(34631),r=i(21820),p=i(55511),c=i(43519),l=i(27910).PassThrough,d=i(91292);class m extends s{constructor(e){super(e),this.id=p.randomBytes(8).toString("base64").replace(/\W/g,""),this.stage="init",this.options=e||{},this.secureConnection=!!this.options.secure,this.alreadySecured=!!this.options.secured,this.port=Number(this.options.port)||(this.secureConnection?465:587),this.host=this.options.host||"localhost",this.servername=this.options.servername?this.options.servername:!n.isIP(this.host)&&this.host,this.allowInternalNetworkInterfaces=this.options.allowInternalNetworkInterfaces||!1,void 0===this.options.secure&&465===this.port&&(this.secureConnection=!0),this.name=this.options.name||this._getHostname(),this.logger=d.getLogger(this.options,{component:this.options.component||"smtp-connection",sid:this.id}),this.customAuth=new Map,Object.keys(this.options.customAuth||{}).forEach(e=>{let t=(e||"").toString().trim().toUpperCase();t&&this.customAuth.set(t,this.options.customAuth[e])}),this.version=a.version,this.authenticated=!1,this.destroyed=!1,this.secure=!!this.secureConnection,this._remainder="",this._responseQueue=[],this.lastServerResponse=!1,this._socket=!1,this._supportedAuth=[],this.allowsAuth=!1,this._envelope=!1,this._supportedExtensions=[],this._maxAllowedSize=0,this._responseActions=[],this._recipientQueue=[],this._greetingTimeout=!1,this._connectionTimeout=!1,this._destroyed=!1,this._closing=!1,this._onSocketData=e=>this._onData(e),this._onSocketError=e=>this._onError(e,"ESOCKET",!1,"CONN"),this._onSocketClose=()=>this._onClose(),this._onSocketEnd=()=>this._onEnd(),this._onSocketTimeout=()=>this._onTimeout()}connect(e){if("function"==typeof e){this.once("connect",()=>{this.logger.debug({tnx:"smtp"},"SMTP handshake finished"),e()});let t=this._isDestroyedMessage("connect");if(t)return e(this._formatError(t,"ECONNECTION",!1,"CONN"))}let t={port:this.port,host:this.host,allowInternalNetworkInterfaces:this.allowInternalNetworkInterfaces,timeout:this.options.dnsTimeout||3e4};this.options.localAddress&&(t.localAddress=this.options.localAddress);let i=()=>{this._connectionTimeout=setTimeout(()=>{this._onError("Connection timeout","ETIMEDOUT",!1,"CONN")},this.options.connectionTimeout||12e4),this._socket.on("error",this._onSocketError)};if(this.options.connection){this._socket=this.options.connection,i(),this.secureConnection&&!this.alreadySecured?setImmediate(()=>this._upgradeConnection(e=>{if(e)return void this._onError(Error("Error initiating TLS - "+(e.message||e)),"ETLS",!1,"CONN");this._onConnect()})):setImmediate(()=>this._onConnect());return}return this.options.socket?(this._socket=this.options.socket,d.resolveHostname(t,(e,a)=>{if(e)return setImmediate(()=>this._onError(e,"EDNS",!1,"CONN"));this.logger.debug({tnx:"dns",source:t.host,resolved:a.host,cached:!!a.cached},"Resolved %s as %s [cache %s]",t.host,a.host,a.cached?"hit":"miss"),Object.keys(a).forEach(e=>{"_"!==e.charAt(0)&&a[e]&&(t[e]=a[e])});try{this._socket.connect(this.port,this.host,()=>{this._socket.setKeepAlive(!0),this._onConnect()}),i()}catch(e){return setImmediate(()=>this._onError(e,"ECONNECTION",!1,"CONN"))}})):this.secureConnection?(this.options.tls&&Object.keys(this.options.tls).forEach(e=>{t[e]=this.options.tls[e]}),this.servername&&!t.servername&&(t.servername=this.servername),d.resolveHostname(t,(e,a)=>{if(e)return setImmediate(()=>this._onError(e,"EDNS",!1,"CONN"));this.logger.debug({tnx:"dns",source:t.host,resolved:a.host,cached:!!a.cached},"Resolved %s as %s [cache %s]",t.host,a.host,a.cached?"hit":"miss"),Object.keys(a).forEach(e=>{"_"!==e.charAt(0)&&a[e]&&(t[e]=a[e])});try{this._socket=o.connect(t,()=>{this._socket.setKeepAlive(!0),this._onConnect()}),i()}catch(e){return setImmediate(()=>this._onError(e,"ECONNECTION",!1,"CONN"))}})):d.resolveHostname(t,(e,a)=>{if(e)return setImmediate(()=>this._onError(e,"EDNS",!1,"CONN"));this.logger.debug({tnx:"dns",source:t.host,resolved:a.host,cached:!!a.cached},"Resolved %s as %s [cache %s]",t.host,a.host,a.cached?"hit":"miss"),Object.keys(a).forEach(e=>{"_"!==e.charAt(0)&&a[e]&&(t[e]=a[e])});try{this._socket=n.connect(t,()=>{this._socket.setKeepAlive(!0),this._onConnect()}),i()}catch(e){return setImmediate(()=>this._onError(e,"ECONNECTION",!1,"CONN"))}})}quit(){this._sendCommand("QUIT"),this._responseActions.push(this.close)}close(){if(clearTimeout(this._connectionTimeout),clearTimeout(this._greetingTimeout),this._responseActions=[],this._closing)return;this._closing=!0;let e="end";"init"===this.stage&&(e="destroy"),this.logger.debug({tnx:"smtp"},'Closing connection to the server using "%s"',e);let t=this._socket&&this._socket.socket||this._socket;if(t&&!t.destroyed)try{t[e]()}catch(e){}this._destroy()}login(e,t){let i=this._isDestroyedMessage("login");if(i)return t(this._formatError(i,"ECONNECTION",!1,"API"));if(this._auth=e||{},this._authMethod=(this._auth.method||"").toString().trim().toUpperCase()||!1,this._authMethod||!this._auth.oauth2||this._auth.credentials?this._authMethod&&("XOAUTH2"!==this._authMethod||this._auth.oauth2)||(this._authMethod=(this._supportedAuth[0]||"PLAIN").toUpperCase().trim()):this._authMethod="XOAUTH2","XOAUTH2"!==this._authMethod&&(!this._auth.credentials||!this._auth.credentials.user||!this._auth.credentials.pass))if(!(this._auth.user&&this._auth.pass||this.customAuth.has(this._authMethod)))return t(this._formatError('Missing credentials for "'+this._authMethod+'"',"EAUTH",!1,"API"));else this._auth.credentials={user:this._auth.user,pass:this._auth.pass,options:this._auth.options};if(this.customAuth.has(this._authMethod)){let e,i=this.customAuth.get(this._authMethod),a=!1,s=()=>{a||(a=!0,this.logger.info({tnx:"smtp",username:this._auth.user,action:"authenticated",method:this._authMethod},"User %s authenticated",JSON.stringify(this._auth.user)),this.authenticated=!0,t(null,!0))},n=i=>{a||(a=!0,t(this._formatError(i,"EAUTH",e,"AUTH "+this._authMethod)))},o=i({auth:this._auth,method:this._authMethod,extensions:[].concat(this._supportedExtensions),authMethods:[].concat(this._supportedAuth),maxAllowedSize:this._maxAllowedSize||!1,sendCommand:(t,i)=>{let a;return i||(a=new Promise((e,t)=>{i=d.callbackPromise(e,t)})),this._responseActions.push(a=>{e=a;let s=a.match(/^(\d+)(?:\s(\d+\.\d+\.\d+))?\s/),n={command:t,response:a};s?(n.status=Number(s[1])||0,s[2]&&(n.code=s[2]),n.text=a.substr(s[0].length)):(n.text=a,n.status=0),i(null,n)}),setImmediate(()=>this._sendCommand(t)),a},resolve:s,reject:n});o&&"function"==typeof o.catch&&o.then(s).catch(n);return}switch(this._authMethod){case"XOAUTH2":this._handleXOauth2Token(!1,t);return;case"LOGIN":this._responseActions.push(e=>{this._actionAUTH_LOGIN_USER(e,t)}),this._sendCommand("AUTH LOGIN");return;case"PLAIN":this._responseActions.push(e=>{this._actionAUTHComplete(e,t)}),this._sendCommand("AUTH PLAIN "+Buffer.from("\0"+this._auth.credentials.user+"\0"+this._auth.credentials.pass,"utf-8").toString("base64"),"AUTH PLAIN "+Buffer.from("\0"+this._auth.credentials.user+"\0/* secret */","utf-8").toString("base64"));return;case"CRAM-MD5":this._responseActions.push(e=>{this._actionAUTH_CRAM_MD5(e,t)}),this._sendCommand("AUTH CRAM-MD5");return}return t(this._formatError('Unknown authentication method "'+this._authMethod+'"',"EAUTH",!1,"API"))}send(e,t,i){if(!t)return i(this._formatError("Empty message","EMESSAGE",!1,"API"));let a=this._isDestroyedMessage("send message");if(a)return i(this._formatError(a,"ECONNECTION",!1,"API"));if(this._maxAllowedSize&&e.size>this._maxAllowedSize)return setImmediate(()=>{i(this._formatError("Message size larger than allowed "+this._maxAllowedSize,"EMESSAGE",!1,"MAIL FROM"))});let s=!1,n=function(){s||(s=!0,i(...arguments))};"function"==typeof t.on&&t.on("error",e=>n(this._formatError(e,"ESTREAM",!1,"API")));let o=Date.now();this._setEnvelope(e,(e,i)=>{if(e){let i=new l;return"function"==typeof t.pipe?t.pipe(i):(i.write(t),i.end()),n(e)}let a=Date.now(),s=this._createSendStream((e,t)=>e?n(e):(i.envelopeTime=a-o,i.messageTime=Date.now()-a,i.messageSize=s.outByteCount,i.response=t,n(null,i)));"function"==typeof t.pipe?t.pipe(s):(s.write(t),s.end())})}reset(e){this._sendCommand("RSET"),this._responseActions.push(t=>"2"!==t.charAt(0)?e(this._formatError("Could not reset session state. response="+t,"EPROTOCOL",t,"RSET")):(this._envelope=!1,e(null,!0)))}_onConnect(){if(clearTimeout(this._connectionTimeout),this.logger.info({tnx:"network",localAddress:this._socket.localAddress,localPort:this._socket.localPort,remoteAddress:this._socket.remoteAddress,remotePort:this._socket.remotePort},"%s established to %s:%s",this.secure?"Secure connection":"Connection",this._socket.remoteAddress,this._socket.remotePort),this._destroyed)return void this.close();this.stage="connected",this._socket.removeListener("data",this._onSocketData),this._socket.removeListener("timeout",this._onSocketTimeout),this._socket.removeListener("close",this._onSocketClose),this._socket.removeListener("end",this._onSocketEnd),this._socket.on("data",this._onSocketData),this._socket.once("close",this._onSocketClose),this._socket.once("end",this._onSocketEnd),this._socket.setTimeout(this.options.socketTimeout||6e5),this._socket.on("timeout",this._onSocketTimeout),this._greetingTimeout=setTimeout(()=>{this._socket&&!this._destroyed&&this._responseActions[0]===this._actionGreeting&&this._onError("Greeting never received","ETIMEDOUT",!1,"CONN")},this.options.greetingTimeout||3e4),this._responseActions.push(this._actionGreeting),this._socket.resume()}_onData(e){let t;if(this._destroyed||!e||!e.length)return;let i=(e||"").toString("binary"),a=(this._remainder+i).split(/\r?\n/);this._remainder=a.pop();for(let e=0,i=a.length;e<i;e++){if(this._responseQueue.length&&(t=this._responseQueue[this._responseQueue.length-1],/^\d+-/.test(t.split("\n").pop()))){this._responseQueue[this._responseQueue.length-1]+="\n"+a[e];continue}this._responseQueue.push(a[e])}this._responseQueue.length&&(t=this._responseQueue[this._responseQueue.length-1],/^\d+-/.test(t.split("\n").pop()))||this._processResponse()}_onError(e,t,i,a){clearTimeout(this._connectionTimeout),clearTimeout(this._greetingTimeout),this._destroyed||(e=this._formatError(e,t,i,a),this.logger.error(i,e.message),this.emit("error",e),this.close())}_formatError(e,t,i,a){let s;s=/Error\]$/i.test(Object.prototype.toString.call(e))?e:Error(e),t&&"Error"!==t&&(s.code=t),i&&(s.response=i,s.message+=": "+i);let n="string"==typeof i&&Number((i.match(/^\d+/)||[])[0])||!1;return n&&(s.responseCode=n),a&&(s.command=a),s}_onClose(){let e=!1;return(this._remainder&&this._remainder.trim()&&((this.options.debug||this.options.transactionLog)&&this.logger.debug({tnx:"server"},this._remainder.replace(/\r?\n$/,"")),this.lastServerResponse=e=this._remainder.trim()),this.logger.info({tnx:"network"},"Connection closed"),this.upgrading&&!this._destroyed)?this._onError(Error("Connection closed unexpectedly"),"ETLS",e,"CONN"):![this._actionGreeting,this.close].includes(this._responseActions[0])&&!this._destroyed||/^[45]\d{2}\b/.test(e)?this._onError(Error("Connection closed unexpectedly"),"ECONNECTION",e,"CONN"):void this._destroy()}_onEnd(){this._socket&&!this._socket.destroyed&&this._socket.destroy()}_onTimeout(){return this._onError(Error("Timeout"),"ETIMEDOUT",!1,"CONN")}_destroy(){this._destroyed||(this._destroyed=!0,this.emit("end"))}_upgradeConnection(e){this._socket.removeListener("data",this._onSocketData),this._socket.removeListener("timeout",this._onSocketTimeout);let t=this._socket,i={socket:this._socket,host:this.host};Object.keys(this.options.tls||{}).forEach(e=>{i[e]=this.options.tls[e]}),this.servername&&!i.servername&&(i.servername=this.servername),this.upgrading=!0;try{this._socket=o.connect(i,()=>(this.secure=!0,this.upgrading=!1,this._socket.on("data",this._onSocketData),t.removeListener("close",this._onSocketClose),t.removeListener("end",this._onSocketEnd),e(null,!0)))}catch(t){return e(t)}this._socket.on("error",this._onSocketError),this._socket.once("close",this._onSocketClose),this._socket.once("end",this._onSocketEnd),this._socket.setTimeout(this.options.socketTimeout||6e5),this._socket.on("timeout",this._onSocketTimeout),t.resume()}_processResponse(){if(!this._responseQueue.length)return!1;let e=this.lastServerResponse=(this._responseQueue.shift()||"").toString();if(/^\d+-/.test(e.split("\n").pop()))return;(this.options.debug||this.options.transactionLog)&&this.logger.debug({tnx:"server"},e.replace(/\r?\n$/,"")),e.trim()||setImmediate(()=>this._processResponse());let t=this._responseActions.shift();if("function"!=typeof t)return this._onError(Error("Unexpected Response"),"EPROTOCOL",e,"CONN");t.call(this,e),setImmediate(()=>this._processResponse())}_sendCommand(e,t){if(!this._destroyed){if(this._socket.destroyed)return this.close();(this.options.debug||this.options.transactionLog)&&this.logger.debug({tnx:"client"},(t||e||"").toString().replace(/\r?\n$/,"")),this._socket.write(Buffer.from(e+"\r\n","utf-8"))}}_setEnvelope(e,t){let i=[],a=!1;if(this._envelope=e||{},this._envelope.from=(this._envelope.from&&this._envelope.from.address||this._envelope.from||"").toString().trim(),this._envelope.to=[].concat(this._envelope.to||[]).map(e=>(e&&e.address||e||"").toString().trim()),!this._envelope.to.length)return t(this._formatError("No recipients defined","EENVELOPE",!1,"API"));if(this._envelope.from&&/[\r\n<>]/.test(this._envelope.from))return t(this._formatError("Invalid sender "+JSON.stringify(this._envelope.from),"EENVELOPE",!1,"API"));/[\x80-\uFFFF]/.test(this._envelope.from)&&(a=!0);for(let e=0,i=this._envelope.to.length;e<i;e++){if(!this._envelope.to[e]||/[\r\n<>]/.test(this._envelope.to[e]))return t(this._formatError("Invalid recipient "+JSON.stringify(this._envelope.to[e]),"EENVELOPE",!1,"API"));/[\x80-\uFFFF]/.test(this._envelope.to[e])&&(a=!0)}if(this._envelope.rcptQueue=JSON.parse(JSON.stringify(this._envelope.to||[])),this._envelope.rejected=[],this._envelope.rejectedErrors=[],this._envelope.accepted=[],this._envelope.dsn)try{this._envelope.dsn=this._setDsnEnvelope(this._envelope.dsn)}catch(e){return t(this._formatError("Invalid DSN "+e.message,"EENVELOPE",!1,"API"))}this._responseActions.push(e=>{this._actionMAIL(e,t)}),a&&this._supportedExtensions.includes("SMTPUTF8")&&(i.push("SMTPUTF8"),this._usingSmtpUtf8=!0),this._envelope.use8BitMime&&this._supportedExtensions.includes("8BITMIME")&&(i.push("BODY=8BITMIME"),this._using8BitMime=!0),this._envelope.size&&this._supportedExtensions.includes("SIZE")&&i.push("SIZE="+this._envelope.size),this._envelope.dsn&&this._supportedExtensions.includes("DSN")&&(this._envelope.dsn.ret&&i.push("RET="+d.encodeXText(this._envelope.dsn.ret)),this._envelope.dsn.envid&&i.push("ENVID="+d.encodeXText(this._envelope.dsn.envid))),this._sendCommand("MAIL FROM:<"+this._envelope.from+">"+(i.length?" "+i.join(" "):""))}_setDsnEnvelope(e){let t=(e.ret||e.return||"").toString().toUpperCase()||null;if(t)switch(t){case"HDRS":case"HEADERS":t="HDRS";break;case"FULL":case"BODY":t="FULL"}if(t&&!["FULL","HDRS"].includes(t))throw Error("ret: "+JSON.stringify(t));let i=(e.envid||e.id||"").toString()||null,a=e.notify||null;if(a){"string"==typeof a&&(a=a.split(","));let e=["NEVER","SUCCESS","FAILURE","DELAY"];if((a=a.map(e=>e.trim().toUpperCase())).filter(t=>!e.includes(t)).length||a.length>1&&a.includes("NEVER"))throw Error("notify: "+JSON.stringify(a.join(",")));a=a.join(",")}let s=(e.recipient||e.orcpt||"").toString()||null;return s&&0>s.indexOf(";")&&(s="rfc822;"+s),{ret:t,envid:i,notify:a,orcpt:s}}_getDsnRcptToArgs(){let e=[];return this._envelope.dsn&&this._supportedExtensions.includes("DSN")&&(this._envelope.dsn.notify&&e.push("NOTIFY="+d.encodeXText(this._envelope.dsn.notify)),this._envelope.dsn.orcpt&&e.push("ORCPT="+d.encodeXText(this._envelope.dsn.orcpt))),e.length?" "+e.join(" "):""}_createSendStream(e){let t,i=new c;return this.options.lmtp?this._envelope.accepted.forEach((t,i)=>{let a=i===this._envelope.accepted.length-1;this._responseActions.push(i=>{this._actionLMTPStream(t,a,i,e)})}):this._responseActions.push(t=>{this._actionSMTPStream(t,e)}),i.pipe(this._socket,{end:!1}),this.options.debug&&((t=new l).on("readable",()=>{let e;for(;e=t.read();)this.logger.debug({tnx:"message"},e.toString("binary").replace(/\r?\n$/,""))}),i.pipe(t)),i.once("end",()=>{this.logger.info({tnx:"message",inByteCount:i.inByteCount,outByteCount:i.outByteCount},"<%s bytes encoded mime message (source size %s bytes)>",i.outByteCount,i.inByteCount)}),i}_actionGreeting(e){if(clearTimeout(this._greetingTimeout),"220"!==e.substr(0,3))return void this._onError(Error("Invalid greeting. response="+e),"EPROTOCOL",e,"CONN");this.options.lmtp?(this._responseActions.push(this._actionLHLO),this._sendCommand("LHLO "+this.name)):(this._responseActions.push(this._actionEHLO),this._sendCommand("EHLO "+this.name))}_actionLHLO(e){if("2"!==e.charAt(0))return void this._onError(Error("Invalid LHLO. response="+e),"EPROTOCOL",e,"LHLO");this._actionEHLO(e)}_actionEHLO(e){let t;if("421"===e.substr(0,3))return void this._onError(Error("Server terminates connection. response="+e),"ECONNECTION",e,"EHLO");if("2"!==e.charAt(0))return this.options.requireTLS?void this._onError(Error("EHLO failed but HELO does not support required STARTTLS. response="+e),"ECONNECTION",e,"EHLO"):(this._responseActions.push(this._actionHELO),void this._sendCommand("HELO "+this.name));if(this._ehloLines=e.split(/\r?\n/).map(e=>e.replace(/^\d+[ -]/,"").trim()).filter(e=>e).slice(1),!this.secure&&!this.options.ignoreTLS&&(/[ -]STARTTLS\b/im.test(e)||this.options.requireTLS)){this._sendCommand("STARTTLS"),this._responseActions.push(this._actionSTARTTLS);return}/[ -]SMTPUTF8\b/im.test(e)&&this._supportedExtensions.push("SMTPUTF8"),/[ -]DSN\b/im.test(e)&&this._supportedExtensions.push("DSN"),/[ -]8BITMIME\b/im.test(e)&&this._supportedExtensions.push("8BITMIME"),/[ -]PIPELINING\b/im.test(e)&&this._supportedExtensions.push("PIPELINING"),/[ -]AUTH\b/i.test(e)&&(this.allowsAuth=!0),/[ -]AUTH(?:(\s+|=)[^\n]*\s+|\s+|=)PLAIN/i.test(e)&&this._supportedAuth.push("PLAIN"),/[ -]AUTH(?:(\s+|=)[^\n]*\s+|\s+|=)LOGIN/i.test(e)&&this._supportedAuth.push("LOGIN"),/[ -]AUTH(?:(\s+|=)[^\n]*\s+|\s+|=)CRAM-MD5/i.test(e)&&this._supportedAuth.push("CRAM-MD5"),/[ -]AUTH(?:(\s+|=)[^\n]*\s+|\s+|=)XOAUTH2/i.test(e)&&this._supportedAuth.push("XOAUTH2"),(t=e.match(/[ -]SIZE(?:[ \t]+(\d+))?/im))&&(this._supportedExtensions.push("SIZE"),this._maxAllowedSize=Number(t[1])||0),this.emit("connect")}_actionHELO(e){if("2"!==e.charAt(0))return void this._onError(Error("Invalid HELO. response="+e),"EPROTOCOL",e,"HELO");this.allowsAuth=!0,this.emit("connect")}_actionSTARTTLS(e){if("2"!==e.charAt(0))return this.options.opportunisticTLS?(this.logger.info({tnx:"smtp"},"Failed STARTTLS upgrade, continuing unencrypted"),this.emit("connect")):void this._onError(Error("Error upgrading connection with STARTTLS"),"ETLS",e,"STARTTLS");this._upgradeConnection((e,t)=>{if(e)return void this._onError(Error("Error initiating TLS - "+(e.message||e)),"ETLS",!1,"STARTTLS");this.logger.info({tnx:"smtp"},"Connection upgraded with STARTTLS"),t?this.options.lmtp?(this._responseActions.push(this._actionLHLO),this._sendCommand("LHLO "+this.name)):(this._responseActions.push(this._actionEHLO),this._sendCommand("EHLO "+this.name)):this.emit("connect")})}_actionAUTH_LOGIN_USER(e,t){if(!/^334[ -]/.test(e))return void t(this._formatError('Invalid login sequence while waiting for "334 VXNlcm5hbWU6"',"EAUTH",e,"AUTH LOGIN"));this._responseActions.push(e=>{this._actionAUTH_LOGIN_PASS(e,t)}),this._sendCommand(Buffer.from(this._auth.credentials.user+"","utf-8").toString("base64"))}_actionAUTH_CRAM_MD5(e,t){let i=e.match(/^334\s+(.+)$/),a="";if(!i)return t(this._formatError("Invalid login sequence while waiting for server challenge string","EAUTH",e,"AUTH CRAM-MD5"));a=i[1];let s=Buffer.from(a,"base64").toString("ascii"),n=p.createHmac("md5",this._auth.credentials.pass);n.update(s);let o=this._auth.credentials.user+" "+n.digest("hex");this._responseActions.push(e=>{this._actionAUTH_CRAM_MD5_PASS(e,t)}),this._sendCommand(Buffer.from(o).toString("base64"),Buffer.from(this._auth.credentials.user+" /* secret */").toString("base64"))}_actionAUTH_CRAM_MD5_PASS(e,t){if(!e.match(/^235\s+/))return t(this._formatError('Invalid login sequence while waiting for "235"',"EAUTH",e,"AUTH CRAM-MD5"));this.logger.info({tnx:"smtp",username:this._auth.user,action:"authenticated",method:this._authMethod},"User %s authenticated",JSON.stringify(this._auth.user)),this.authenticated=!0,t(null,!0)}_actionAUTH_LOGIN_PASS(e,t){if(!/^334[ -]/.test(e))return t(this._formatError('Invalid login sequence while waiting for "334 UGFzc3dvcmQ6"',"EAUTH",e,"AUTH LOGIN"));this._responseActions.push(e=>{this._actionAUTHComplete(e,t)}),this._sendCommand(Buffer.from((this._auth.credentials.pass||"").toString(),"utf-8").toString("base64"),Buffer.from("/* secret */","utf-8").toString("base64"))}_actionAUTHComplete(e,t,i){if(i||"function"!=typeof t||(i=t,t=!1),"334"===e.substr(0,3)){this._responseActions.push(e=>{t||"XOAUTH2"!==this._authMethod?this._actionAUTHComplete(e,!0,i):setImmediate(()=>this._handleXOauth2Token(!0,i))}),this._sendCommand("");return}if("2"!==e.charAt(0))return this.logger.info({tnx:"smtp",username:this._auth.user,action:"authfail",method:this._authMethod},"User %s failed to authenticate",JSON.stringify(this._auth.user)),i(this._formatError("Invalid login","EAUTH",e,"AUTH "+this._authMethod));this.logger.info({tnx:"smtp",username:this._auth.user,action:"authenticated",method:this._authMethod},"User %s authenticated",JSON.stringify(this._auth.user)),this.authenticated=!0,i(null,!0)}_actionMAIL(e,t){let i,a;if(2!==Number(e.charAt(0)))return i=this._usingSmtpUtf8&&/^550 /.test(e)&&/[\x80-\uFFFF]/.test(this._envelope.from)?"Internationalized mailbox name not allowed":"Mail command failed",t(this._formatError(i,"EENVELOPE",e,"MAIL FROM"));if(!this._envelope.rcptQueue.length)return t(this._formatError("Can't send mail - no recipients defined","EENVELOPE",!1,"API"));if(this._recipientQueue=[],this._supportedExtensions.includes("PIPELINING"))for(;this._envelope.rcptQueue.length;)a=this._envelope.rcptQueue.shift(),this._recipientQueue.push(a),this._responseActions.push(e=>{this._actionRCPT(e,t)}),this._sendCommand("RCPT TO:<"+a+">"+this._getDsnRcptToArgs());else a=this._envelope.rcptQueue.shift(),this._recipientQueue.push(a),this._responseActions.push(e=>{this._actionRCPT(e,t)}),this._sendCommand("RCPT TO:<"+a+">"+this._getDsnRcptToArgs())}_actionRCPT(e,t){let i,a,s=this._recipientQueue.shift();if(2!==Number(e.charAt(0))?(i=this._usingSmtpUtf8&&/^553 /.test(e)&&/[\x80-\uFFFF]/.test(s)?"Internationalized mailbox name not allowed":"Recipient command failed",this._envelope.rejected.push(s),(a=this._formatError(i,"EENVELOPE",e,"RCPT TO")).recipient=s,this._envelope.rejectedErrors.push(a)):this._envelope.accepted.push(s),this._envelope.rcptQueue.length||this._recipientQueue.length)this._envelope.rcptQueue.length&&(s=this._envelope.rcptQueue.shift(),this._recipientQueue.push(s),this._responseActions.push(e=>{this._actionRCPT(e,t)}),this._sendCommand("RCPT TO:<"+s+">"+this._getDsnRcptToArgs()));else{if(!(this._envelope.rejected.length<this._envelope.to.length))return(a=this._formatError("Can't send mail - all recipients were rejected","EENVELOPE",e,"RCPT TO")).rejected=this._envelope.rejected,a.rejectedErrors=this._envelope.rejectedErrors,t(a);this._responseActions.push(e=>{this._actionDATA(e,t)}),this._sendCommand("DATA")}}_actionDATA(e,t){if(!/^[23]/.test(e))return t(this._formatError("Data command failed","EENVELOPE",e,"DATA"));let i={accepted:this._envelope.accepted,rejected:this._envelope.rejected};this._ehloLines&&this._ehloLines.length&&(i.ehlo=this._ehloLines),this._envelope.rejectedErrors.length&&(i.rejectedErrors=this._envelope.rejectedErrors),t(null,i)}_actionSMTPStream(e,t){return 2!==Number(e.charAt(0))?t(this._formatError("Message failed","EMESSAGE",e,"DATA")):t(null,e)}_actionLMTPStream(e,t,i,a){let s;if(2!==Number(i.charAt(0))){(s=this._formatError("Message failed for recipient "+e,"EMESSAGE",i,"DATA")).recipient=e,this._envelope.rejected.push(e),this._envelope.rejectedErrors.push(s);for(let t=0,i=this._envelope.accepted.length;t<i;t++)this._envelope.accepted[t]===e&&this._envelope.accepted.splice(t,1)}if(t)return a(null,i)}_handleXOauth2Token(e,t){this._auth.oauth2.getToken(e,(i,a)=>{if(i)return this.logger.info({tnx:"smtp",username:this._auth.user,action:"authfail",method:this._authMethod},"User %s failed to authenticate",JSON.stringify(this._auth.user)),t(this._formatError(i,"EAUTH",!1,"AUTH XOAUTH2"));this._responseActions.push(i=>{this._actionAUTHComplete(i,e,t)}),this._sendCommand("AUTH XOAUTH2 "+this._auth.oauth2.buildXOAuth2Token(a),"AUTH XOAUTH2 "+this._auth.oauth2.buildXOAuth2Token("/* secret */"))})}_isDestroyedMessage(e){if(this._destroyed)return"Cannot "+e+" - smtp connection is already destroyed.";if(this._socket){if(this._socket.destroyed)return"Cannot "+e+" - smtp connection socket is already destroyed.";if(!this._socket.writable)return"Cannot "+e+" - smtp connection socket is already half-closed."}}_getHostname(){let e;try{e=r.hostname()||""}catch(t){e="localhost"}return(!e||0>e.indexOf("."))&&(e="[127.0.0.1]"),e.match(/^\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}$/)&&(e="["+e+"]"),e}}e.exports=m},21820:e=>{"use strict";e.exports=require("os")},21940:(e,t,i)=>{"use strict";let a=i(27910).Stream,s=i(38099),n=i(55511),o=i(91292);class r extends a{constructor(e,t){if(super(),this.options=e||{},e&&e.serviceClient){if(!e.privateKey||!e.user)return void setImmediate(()=>this.emit("error",Error('Options "privateKey" and "user" are required for service account!')));let t=Math.min(Math.max(Number(this.options.serviceRequestTimeout)||0,0),3600);this.options.serviceRequestTimeout=t||300}if(this.logger=o.getLogger({logger:t},{component:this.options.component||"OAuth2"}),this.provisionCallback="function"==typeof this.options.provisionCallback&&this.options.provisionCallback,this.options.accessUrl=this.options.accessUrl||"https://accounts.google.com/o/oauth2/token",this.options.customHeaders=this.options.customHeaders||{},this.options.customParams=this.options.customParams||{},this.accessToken=this.options.accessToken||!1,this.options.expires&&Number(this.options.expires))this.expires=this.options.expires;else{let e=Math.max(Number(this.options.timeout)||0,0);this.expires=e&&Date.now()+1e3*e||0}}getToken(e,t){if(!e&&this.accessToken&&(!this.expires||this.expires>Date.now()))return t(null,this.accessToken);let i=(...e)=>{e[0]?this.logger.error({err:e[0],tnx:"OAUTH2",user:this.options.user,action:"renew"},"Failed generating new Access Token for %s",this.options.user):this.logger.info({tnx:"OAUTH2",user:this.options.user,action:"renew"},"Generated new Access Token for %s",this.options.user),t(...e)};this.provisionCallback?this.provisionCallback(this.options.user,!!e,(e,t,a)=>{!e&&t&&(this.accessToken=t,this.expires=a||0),i(e,t)}):this.generateToken(i)}updateToken(e,t){this.accessToken=e,t=Math.max(Number(t)||0,0),this.expires=t&&Date.now()+1e3*t||0,this.emit("token",{user:this.options.user,accessToken:e||"",expires:this.expires})}generateToken(e){let t,i;if(this.options.serviceClient){let a,s=Math.floor(Date.now()/1e3),n={iss:this.options.serviceClient,scope:this.options.scope||"https://mail.google.com/",sub:this.options.user,aud:this.options.accessUrl,iat:s,exp:s+this.options.serviceRequestTimeout};try{a=this.jwtSignRS256(n)}catch(t){return e(Error("Can't generate token. Check your auth options"))}t={grant_type:"urn:ietf:params:oauth:grant-type:jwt-bearer",assertion:a},i={grant_type:"urn:ietf:params:oauth:grant-type:jwt-bearer",assertion:n}}else{if(!this.options.refreshToken)return e(Error("Can't create new access token for user"));t={client_id:this.options.clientId||"",client_secret:this.options.clientSecret||"",refresh_token:this.options.refreshToken,grant_type:"refresh_token"},i={client_id:this.options.clientId||"",client_secret:(this.options.clientSecret||"").substr(0,6)+"...",refresh_token:(this.options.refreshToken||"").substr(0,6)+"...",grant_type:"refresh_token"}}Object.keys(this.options.customParams).forEach(e=>{t[e]=this.options.customParams[e],i[e]=this.options.customParams[e]}),this.logger.debug({tnx:"OAUTH2",user:this.options.user,action:"generate"},"Requesting token using: %s",JSON.stringify(i)),this.postRequest(this.options.accessUrl,t,this.options,(t,i)=>{let a;if(t)return e(t);try{a=JSON.parse(i.toString())}catch(t){return e(t)}if(!a||"object"!=typeof a)return this.logger.debug({tnx:"OAUTH2",user:this.options.user,action:"post"},"Response: %s",(i||"").toString()),e(Error("Invalid authentication response"));let s={};if(Object.keys(a).forEach(e=>{"access_token"!==e?s[e]=a[e]:s[e]=(a[e]||"").toString().substr(0,6)+"..."}),this.logger.debug({tnx:"OAUTH2",user:this.options.user,action:"post"},"Response: %s",JSON.stringify(s)),a.error){let t=a.error;return a.error_description&&(t+=": "+a.error_description),a.error_uri&&(t+=" ("+a.error_uri+")"),e(Error(t))}return a.access_token?(this.updateToken(a.access_token,a.expires_in),e(null,this.accessToken)):e(Error("No access token"))})}buildXOAuth2Token(e){let t=["user="+(this.options.user||""),"auth=Bearer "+(e||this.accessToken),"",""];return Buffer.from(t.join("\x01"),"utf-8").toString("base64")}postRequest(e,t,i,a){let n=!1,o=[],r=0,p=s(e,{method:"post",headers:i.customHeaders,body:t,allowErrorResponse:!0});p.on("readable",()=>{let e;for(;null!==(e=p.read());)o.push(e),r+=e.length}),p.once("error",e=>{if(!n)return n=!0,a(e)}),p.once("end",()=>{if(!n)return n=!0,a(null,Buffer.concat(o,r))})}toBase64URL(e){return"string"==typeof e&&(e=Buffer.from(e)),e.toString("base64").replace(/[=]+/g,"").replace(/\+/g,"-").replace(/\//g,"_")}jwtSignRS256(e){e=['{"alg":"RS256","typ":"JWT"}',JSON.stringify(e)].map(e=>this.toBase64URL(e)).join(".");let t=n.createSign("RSA-SHA256").update(e).sign(this.options.privateKey);return e+"."+this.toBase64URL(t)}}e.exports=r},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31534:(e,t,i)=>{"use strict";let a=i(27910).Transform,s=i(55511);class n extends a{constructor(e){super(),e=e||{},this.chunkBuffer=[],this.chunkBufferLen=0,this.bodyHash=s.createHash(e.hashAlgo||"sha1"),this.remainder="",this.byteLength=0,this.debug=e.debug,this._debugBody=!!e.debug&&[]}updateHash(e){let t,i="",a="file";for(let t=e.length-1;t>=0;t--){let s=e[t];if("file"===a&&(10===s||13===s));else if("file"===a&&(9===s||32===s))a="line";else if("line"===a&&(9===s||32===s));else if(("file"===a||"line"===a)&&(a="body",t===e.length-1))break;if(0===t){if("file"===a&&(!this.remainder||/[\r\n]$/.test(this.remainder))||"line"===a&&(!this.remainder||/[ \t]$/.test(this.remainder))){this.remainder+=e.toString("binary");return}else if("line"===a||"file"===a){i=e.toString("binary"),e=!1;break}}if("body"===a){i=e.slice(t+1).toString("binary"),e=e.slice(0,t+1);break}}let s=!!this.remainder;if(e&&!s){for(let t=0,i=e.length;t<i;t++)if(t&&10===e[t]&&13!==e[t-1]){s=!0;break}else if(t&&13===e[t]&&32===e[t-1]){s=!0;break}else if(t&&32===e[t]&&32===e[t-1]){s=!0;break}else if(9===e[t]){s=!0;break}}s?(t=this.remainder+(e?e.toString("binary"):""),this.remainder=i,t=t.replace(/\r?\n/g,"\n").replace(/[ \t]*$/gm,"").replace(/[ \t]+/gm," ").replace(/\n/g,"\r\n"),e=Buffer.from(t,"binary")):i&&(this.remainder=i),this.debug&&this._debugBody.push(e),this.bodyHash.update(e)}_transform(e,t,i){if(!e||!e.length)return i();"string"==typeof e&&(e=Buffer.from(e,t)),this.updateHash(e),this.byteLength+=e.length,this.push(e),i()}_flush(e){/[\r\n]$/.test(this.remainder)&&this.byteLength>2&&this.bodyHash.update(Buffer.from("\r\n")),this.byteLength||this.push(Buffer.from("\r\n")),this.emit("hash",this.bodyHash.digest("base64"),!!this.debug&&Buffer.concat(this._debugBody)),e()}}e.exports=n},33873:e=>{"use strict";e.exports=require("path")},34041:(e,t,i)=>{"use strict";let a=i(27910).Transform;class s extends a{constructor(e){super(e),this.lastBytes=Buffer.alloc(4),this.headersParsed=!1,this.headerBytes=0,this.headerChunks=[],this.rawHeaders=!1,this.bodySize=0}updateLastBytes(e){let t=this.lastBytes.length,i=Math.min(e.length,t);for(let e=0,a=t-i;e<a;e++)this.lastBytes[e]=this.lastBytes[e+i];for(let a=1;a<=i;a++)this.lastBytes[t-a]=e[e.length-a]}checkHeaders(e){if(this.headersParsed)return!0;let t=this.lastBytes.length,i=0;this.curLinePos=0;for(let a=0,s=this.lastBytes.length+e.length;a<s;a++){let s;if(10===(a<t?this.lastBytes[a]:e[a-t])&&a){let s=a-1<t?this.lastBytes[a-1]:e[a-1-t],n=a>1&&(a-2<t?this.lastBytes[a-2]:e[a-2-t]);if(10===s||13===s&&10===n){this.headersParsed=!0,i=a-t+1,this.headerBytes+=i;break}}}if(this.headersParsed){if(this.headerChunks.push(e.slice(0,i)),this.rawHeaders=Buffer.concat(this.headerChunks,this.headerBytes),this.headerChunks=null,this.emit("headers",this.parseHeaders()),e.length-1>i){let t=e.slice(i);this.bodySize+=t.length,setImmediate(()=>this.push(t))}return!1}return this.headerBytes+=e.length,this.headerChunks.push(e),this.updateLastBytes(e),!1}_transform(e,t,i){let a;if(!e||!e.length)return i();"string"==typeof e&&(e=Buffer.from(e,t));try{a=this.checkHeaders(e)}catch(e){return i(e)}a&&(this.bodySize+=e.length,this.push(e)),setImmediate(i)}_flush(e){if(this.headerChunks){let e=Buffer.concat(this.headerChunks,this.headerBytes);this.bodySize+=e.length,this.push(e),this.headerChunks=null}e()}parseHeaders(){let e=(this.rawHeaders||"").toString().split(/\r?\n/);for(let t=e.length-1;t>0;t--)/^\s/.test(e[t])&&(e[t-1]+="\n"+e[t],e.splice(t,1));return e.filter(e=>e.trim()).map(e=>({key:e.substr(0,e.indexOf(":")).trim().toLowerCase(),line:e}))}}e.exports=s},34631:e=>{"use strict";e.exports=require("tls")},38099:(e,t,i)=>{"use strict";let a=i(81630),s=i(55591),n=i(79551),o=i(74075),r=i(27910).PassThrough,p=i(53752),c=i(49074),l=i(91645);e.exports=function(e,t){return function e(t,i){let d,m,h;(i=i||{}).fetchRes=i.fetchRes||new r,i.cookies=i.cookies||new p,i.redirects=i.redirects||0,i.maxRedirects=isNaN(i.maxRedirects)?5:i.maxRedirects,i.cookie&&([].concat(i.cookie||[]).forEach(e=>{i.cookies.set(e,t)}),i.cookie=!1);let u=i.fetchRes,f=n.parse(t),x=(i.method||"").toString().trim().toUpperCase()||"GET",g=!1,v="https:"===f.protocol?s:a,_={"accept-encoding":"gzip,deflate","user-agent":"nodemailer/"+c.version};if(Object.keys(i.headers||{}).forEach(e=>{_[e.toLowerCase().trim()]=i.headers[e]}),i.userAgent&&(_["user-agent"]=i.userAgent),f.auth&&(_.Authorization="Basic "+Buffer.from(f.auth).toString("base64")),(d=i.cookies.get(t))&&(_.cookie=d),i.body){if(!1!==i.contentType&&(_["Content-Type"]=i.contentType||"application/x-www-form-urlencoded"),"function"==typeof i.body.pipe)_["Transfer-Encoding"]="chunked",(m=i.body).on("error",e=>{g||(g=!0,e.type="FETCH",e.sourceUrl=t,u.emit("error",e))});else{if(i.body instanceof Buffer)m=i.body;else if("object"==typeof i.body)try{m=Buffer.from(Object.keys(i.body).map(e=>{let t=i.body[e].toString().trim();return encodeURIComponent(e)+"="+encodeURIComponent(t)}).join("&"))}catch(e){if(g)return;g=!0,e.type="FETCH",e.sourceUrl=t,u.emit("error",e);return}else m=Buffer.from(i.body.toString().trim());_["Content-Type"]=i.contentType||"application/x-www-form-urlencoded",_["Content-Length"]=m.length}x=(i.method||"").toString().trim().toUpperCase()||"POST"}let b={method:x,host:f.hostname,path:f.path,port:f.port?f.port:"https:"===f.protocol?443:80,headers:_,rejectUnauthorized:!1,agent:!1};i.tls&&Object.keys(i.tls).forEach(e=>{b[e]=i.tls[e]}),"https:"!==f.protocol||!f.hostname||f.hostname===b.host||l.isIP(f.hostname)||b.servername||(b.servername=f.hostname);try{h=v.request(b)}catch(e){return g=!0,setImmediate(()=>{e.type="FETCH",e.sourceUrl=t,u.emit("error",e)}),u}return i.timeout&&h.setTimeout(i.timeout,()=>{if(g)return;g=!0,h.abort();let e=Error("Request Timeout");e.type="FETCH",e.sourceUrl=t,u.emit("error",e)}),h.on("error",e=>{g||(g=!0,e.type="FETCH",e.sourceUrl=t,u.emit("error",e))}),h.on("response",a=>{let s;if(!g){switch(a.headers["content-encoding"]){case"gzip":case"deflate":s=o.createUnzip()}if(a.headers["set-cookie"]&&[].concat(a.headers["set-cookie"]||[]).forEach(e=>{i.cookies.set(e,t)}),[301,302,303,307,308].includes(a.statusCode)&&a.headers.location){if(i.redirects++,i.redirects>i.maxRedirects){g=!0;let e=Error("Maximum redirect count exceeded");e.type="FETCH",e.sourceUrl=t,u.emit("error",e),h.abort();return}return i.method="GET",i.body=!1,e(n.resolve(t,a.headers.location),i)}if(u.statusCode=a.statusCode,u.headers=a.headers,a.statusCode>=300&&!i.allowErrorResponse){g=!0;let e=Error("Invalid status code "+a.statusCode);e.type="FETCH",e.sourceUrl=t,u.emit("error",e),h.abort();return}a.on("error",e=>{g||(g=!0,e.type="FETCH",e.sourceUrl=t,u.emit("error",e),h.abort())}),s?(a.pipe(s).pipe(u),s.on("error",e=>{g||(g=!0,e.type="FETCH",e.sourceUrl=t,u.emit("error",e),h.abort())})):a.pipe(u)}}),setImmediate(()=>{if(m)try{if("function"==typeof m.pipe)return m.pipe(h);h.write(m)}catch(e){g=!0,e.type="FETCH",e.sourceUrl=t,u.emit("error",e);return}h.end()}),u}(e,t)},e.exports.Cookies=p},39449:(e,t,i)=>{"use strict";let a=i(27910).Transform;class s extends a{constructor(e){super(e),this.options=e||{},this.lastByte=!1}_transform(e,t,i){let a,s=0;for(let t=0,i=e.length;t<i;t++)10===e[t]&&(t&&13!==e[t-1]||!t&&13!==this.lastByte)&&(t>s&&(a=e.slice(s,t),this.push(a)),this.push(Buffer.from("\r\n")),s=t+1);s&&s<e.length?(a=e.slice(s),this.push(a)):s||this.push(e),this.lastByte=e[e.length-1],i()}}e.exports=s},41748:(e,t,i)=>{"use strict";let a=i(27910).Transform;class s extends a{constructor(e){super(e),this.options=e||{}}_transform(e,t,i){let a,s=0;for(let t=0,i=e.length;t<i;t++)13===e[t]&&(a=e.slice(s,t),s=t+1,this.push(a));s&&s<e.length?(a=e.slice(s),this.push(a)):s||this.push(e),i()}}e.exports=s},43519:(e,t,i)=>{"use strict";let a=i(27910).Transform;class s extends a{constructor(e){super(e),this.options=e||{},this._curLine="",this.inByteCount=0,this.outByteCount=0,this.lastByte=!1}_transform(e,t,i){let a,s=[],n=0,o,r,p=0;if(!e||!e.length)return i();for("string"==typeof e&&(e=Buffer.from(e)),this.inByteCount+=e.length,o=0,r=e.length;o<r;o++)46===e[o]?(!o||10!==e[o-1])&&(o||this.lastByte&&10!==this.lastByte)||(a=e.slice(p,o+1),s.push(a),s.push(Buffer.from(".")),n+=a.length+1,p=o+1):10===e[o]&&(o&&13!==e[o-1]||!o&&13!==this.lastByte)&&(o>p?(a=e.slice(p,o),s.push(a),n+=a.length+2):n+=2,s.push(Buffer.from("\r\n")),p=o+1);n?(p<e.length&&(a=e.slice(p),s.push(a),n+=a.length),this.outByteCount+=n,this.push(Buffer.concat(s,n))):(this.outByteCount+=e.length,this.push(e)),this.lastByte=e[e.length-1],i()}_flush(e){let t;t=10===this.lastByte?Buffer.from(".\r\n"):13===this.lastByte?Buffer.from("\n.\r\n"):Buffer.from("\r\n.\r\n"),this.outByteCount+=t.length,this.push(t),e()}}e.exports=s},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},46945:(e,t,i)=>{"use strict";let a=i(94735),s=i(91292),n=i(50143),o=i(48359),r=i(91570),p=i(7575),c=i(28354),l=i(79551),d=i(49074),m=i(17704),h=i(91645),u=i(14985),f=i(55511);class x extends a{constructor(e,t,i){super(),this.options=t||{},this._defaults=i||{},this._defaultPlugins={compile:[(...e)=>this._convertDataImages(...e)],stream:[]},this._userPlugins={compile:[],stream:[]},this.meta=new Map,this.dkim=!!this.options.dkim&&new r(this.options.dkim),this.transporter=e,this.transporter.mailer=this,this.logger=s.getLogger(this.options,{component:this.options.component||"mail"}),this.logger.debug({tnx:"create"},"Creating transport: %s",this.getVersionString()),"function"==typeof this.transporter.on&&(this.transporter.on("log",e=>{this.logger.debug({tnx:"transport"},"%s: %s",e.type,e.message)}),this.transporter.on("error",e=>{this.logger.error({err:e,tnx:"transport"},"Transport Error: %s",e.message),this.emit("error",e)}),this.transporter.on("idle",(...e)=>{this.emit("idle",...e)})),["close","isIdle","verify"].forEach(e=>{this[e]=(...t)=>"function"==typeof this.transporter[e]?("verify"===e&&"function"==typeof this.getSocket&&(this.transporter.getSocket=this.getSocket,this.getSocket=!1),this.transporter[e](...t)):(this.logger.warn({tnx:"transport",methodName:e},"Non existing method %s called for transport",e),!1)}),this.options.proxy&&"string"==typeof this.options.proxy&&this.setupProxy(this.options.proxy)}use(e,t){return e=(e||"").toString(),this._userPlugins.hasOwnProperty(e)?this._userPlugins[e].push(t):this._userPlugins[e]=[t],this}sendMail(e,t=null){let i;t||(i=new Promise((e,i)=>{t=s.callbackPromise(e,i)})),"function"==typeof this.getSocket&&(this.transporter.getSocket=this.getSocket,this.getSocket=!1);let a=new m(this,e);return this.logger.debug({tnx:"transport",name:this.transporter.name,version:this.transporter.version,action:"send"},"Sending mail using %s/%s",this.transporter.name,this.transporter.version),this._processPlugins("compile",a,e=>{if(e)return this.logger.error({err:e,tnx:"plugin",action:"compile"},"PluginCompile Error: %s",e.message),t(e);a.message=new o(a.data).compile(),a.setMailerHeader(),a.setPriorityHeaders(),a.setListHeaders(),this._processPlugins("stream",a,e=>{if(e)return this.logger.error({err:e,tnx:"plugin",action:"stream"},"PluginStream Error: %s",e.message),t(e);(a.data.dkim||this.dkim)&&a.message.processFunc(e=>{let t=a.data.dkim?new r(a.data.dkim):this.dkim;return this.logger.debug({tnx:"DKIM",messageId:a.message.messageId(),dkimDomains:t.keys.map(e=>e.keySelector+"."+e.domainName).join(", ")},"Signing outgoing message with %s keys",t.keys.length),t.sign(e,a.data._dkim)}),this.transporter.send(a,(...e)=>{e[0]&&this.logger.error({err:e[0],tnx:"transport",action:"send"},"Send Error: %s",e[0].message),t(...e)})})}),i}getVersionString(){return c.format("%s (%s; +%s; %s/%s)",d.name,d.version,d.homepage,this.transporter.name,this.transporter.version)}_processPlugins(e,t,i){if(e=(e||"").toString(),!this._userPlugins.hasOwnProperty(e))return i();let a=this._userPlugins[e]||[],s=this._defaultPlugins[e]||[];if(a.length&&this.logger.debug({tnx:"transaction",pluginCount:a.length,step:e},"Using %s plugins for %s",a.length,e),a.length+s.length===0)return i();let n=0,o="default",r=()=>{let e="default"===o?s:a;if(n>=e.length)if("default"!==o||!a.length)return i();else o="user",n=0,e=a;(0,e[n++])(t,e=>{if(e)return i(e);r()})};r()}setupProxy(e){let t=l.parse(e);this.getSocket=(e,i)=>{let a=t.protocol.replace(/:$/,"").toLowerCase();if(this.meta.has("proxy_handler_"+a))return this.meta.get("proxy_handler_"+a)(t,e,i);switch(a){case"http":case"https":p(t.href,e.port,e.host,(e,t)=>e?i(e):i(null,{connection:t}));return;case"socks":case"socks5":case"socks4":case"socks4a":{if(!this.meta.has("proxy_socks_module"))return i(Error("Socks module not loaded"));let a=a=>{let s=!!this.meta.get("proxy_socks_module").SocksClient,n=s?this.meta.get("proxy_socks_module").SocksClient:this.meta.get("proxy_socks_module"),o=Number(t.protocol.replace(/\D/g,""))||5,r={proxy:{ipaddress:a,port:Number(t.port),type:o},[s?"destination":"target"]:{host:e.host,port:e.port},command:"connect"};if(t.auth){let e=decodeURIComponent(t.auth.split(":").shift()),i=decodeURIComponent(t.auth.split(":").pop());s?(r.proxy.userId=e,r.proxy.password=i):4===o?r.userid=e:r.authentication={username:e,password:i}}n.createConnection(r,(e,t)=>e?i(e):i(null,{connection:t.socket||t}))};if(h.isIP(t.hostname))return a(t.hostname);return u.resolve(t.hostname,(e,t)=>{if(e)return i(e);a(Array.isArray(t)?t[0]:t)})}}i(Error("Unknown proxy configuration"))}}_convertDataImages(e,t){if(!this.options.attachDataUrls&&!e.data.attachDataUrls||!e.data.html)return t();e.resolveContent(e.data,"html",(i,a)=>{if(i)return t(i);let s=0;a=(a||"").toString().replace(/(<img\b[^<>]{0,1024} src\s{0,20}=[\s"']{0,20})(data:([^;]+);[^"'>\s]+)/gi,(t,i,a,o)=>{let r=f.randomBytes(10).toString("hex")+"@localhost";return e.data.attachments||(e.data.attachments=[]),Array.isArray(e.data.attachments)||(e.data.attachments=[].concat(e.data.attachments||[])),e.data.attachments.push({path:a,cid:r,filename:"image-"+ ++s+"."+n.detectExtension(o)}),i+"cid:"+r}),e.data.html=a,t()})}set(e,t){return this.meta.set(e,t)}get(e){return this.meta.get(e)}}e.exports=x},48178:e=>{"use strict";let t=/^xn--/,i=/[^\0-\x7F]/,a=/[\x2E\u3002\uFF0E\uFF61]/g,s={overflow:"Overflow: input needs wider integers to process","not-basic":"Illegal input >= 0x80 (not a basic code point)","invalid-input":"Invalid input"},n=Math.floor,o=String.fromCharCode;function r(e){throw RangeError(s[e])}function p(e,t){let i=e.split("@"),s="";return i.length>1&&(s=i[0]+"@",e=i[1]),s+(function(e,t){let i=[],a=e.length;for(;a--;)i[a]=t(e[a]);return i})((e=e.replace(a,".")).split("."),t).join(".")}function c(e){let t=[],i=0,a=e.length;for(;i<a;){let s=e.charCodeAt(i++);if(s>=55296&&s<=56319&&i<a){let a=e.charCodeAt(i++);(64512&a)==56320?t.push(((1023&s)<<10)+(1023&a)+65536):(t.push(s),i--)}else t.push(s)}return t}let l=function(e,t){return e+22+75*(e<26)-((0!=t)<<5)},d=function(e,t,i){let a=0;for(e=i?n(e/700):e>>1,e+=n(e/t);e>455;a+=36)e=n(e/35);return n(a+36*e/(e+38))},m=function(e){let t=[],i=e.length,a=0,s=128,o=72,p=e.lastIndexOf("-");p<0&&(p=0);for(let i=0;i<p;++i)e.charCodeAt(i)>=128&&r("not-basic"),t.push(e.charCodeAt(i));for(let l=p>0?p+1:0;l<i;){let p=a;for(let t=1,s=36;;s+=36){var c;l>=i&&r("invalid-input");let p=(c=e.charCodeAt(l++))>=48&&c<58?26+(c-48):c>=65&&c<91?c-65:c>=97&&c<123?c-97:36;p>=36&&r("invalid-input"),p>n((0x7fffffff-a)/t)&&r("overflow"),a+=p*t;let d=s<=o?1:s>=o+26?26:s-o;if(p<d)break;let m=36-d;t>n(0x7fffffff/m)&&r("overflow"),t*=m}let m=t.length+1;o=d(a-p,m,0==p),n(a/m)>0x7fffffff-s&&r("overflow"),s+=n(a/m),a%=m,t.splice(a++,0,s)}return String.fromCodePoint(...t)},h=function(e){let t=[],i=(e=c(e)).length,a=128,s=0,p=72;for(let i of e)i<128&&t.push(o(i));let m=t.length,h=m;for(m&&t.push("-");h<i;){let i=0x7fffffff;for(let t of e)t>=a&&t<i&&(i=t);let c=h+1;for(let u of(i-a>n((0x7fffffff-s)/c)&&r("overflow"),s+=(i-a)*c,a=i,e))if(u<a&&++s>0x7fffffff&&r("overflow"),u===a){let e=s;for(let i=36;;i+=36){let a=i<=p?1:i>=p+26?26:i-p;if(e<a)break;let s=e-a,r=36-a;t.push(o(l(a+s%r,0))),e=n(s/r)}t.push(o(l(e,0))),p=d(s,c,h===m),s=0,++h}++s,++a}return t.join("")};e.exports={version:"2.3.1",ucs2:{decode:c,encode:e=>String.fromCodePoint(...e)},decode:m,encode:h,toASCII:function(e){return p(e,function(e){return i.test(e)?"xn--"+h(e):e})},toUnicode:function(e){return p(e,function(e){return t.test(e)?m(e.slice(4).toLowerCase()):e})}}},48359:(e,t,i)=>{"use strict";let a=i(7180),s=i(18199),n=i(91292).parseDataURI;class o{constructor(e){this.mail=e||{},this.message=!1}compile(){return this._alternatives=this.getAlternatives(),this._htmlNode=this._alternatives.filter(e=>/^text\/html\b/i.test(e.contentType)).pop(),this._attachments=this.getAttachments(!!this._htmlNode),this._useRelated=!!(this._htmlNode&&this._attachments.related.length),this._useAlternative=this._alternatives.length>1,this._useMixed=this._attachments.attached.length>1||this._alternatives.length&&1===this._attachments.attached.length,this.mail.raw?this.message=new a("message/rfc822",{newline:this.mail.newline}).setRaw(this.mail.raw):this._useMixed?this.message=this._createMixed():this._useAlternative?this.message=this._createAlternative():this._useRelated?this.message=this._createRelated():this.message=this._createContentNode(!1,[].concat(this._alternatives||[]).concat(this._attachments.attached||[]).shift()||{contentType:"text/plain",content:""}),this.mail.headers&&this.message.addHeader(this.mail.headers),["from","sender","to","cc","bcc","reply-to","in-reply-to","references","subject","message-id","date"].forEach(e=>{let t=e.replace(/-(\w)/g,(e,t)=>t.toUpperCase());this.mail[t]&&this.message.setHeader(e,this.mail[t])}),this.mail.envelope&&this.message.setEnvelope(this.mail.envelope),this.message.messageId(),this.message}getAttachments(e){let t,i,a=[].concat(this.mail.attachments||[]).map((e,t)=>{let i,a=/^message\//i.test(e.contentType);/^data:/i.test(e.path||e.href)&&(e=this._processDataUrl(e));let n=e.contentType||s.detectMimeType(e.filename||e.path||e.href||"bin"),o=/^image\//i.test(n);return i={contentType:n,contentDisposition:e.contentDisposition||(a||o&&e.cid?"inline":"attachment"),contentTransferEncoding:"contentTransferEncoding"in e?e.contentTransferEncoding:"base64"},e.filename?i.filename=e.filename:!a&&!1!==e.filename&&(i.filename=(e.path||e.href||"").split("/").pop().split("?").shift()||"attachment-"+(t+1),0>i.filename.indexOf(".")&&(i.filename+="."+s.detectExtension(i.contentType))),/^https?:\/\//i.test(e.path)&&(e.href=e.path,e.path=void 0),e.cid&&(i.cid=e.cid),e.raw?i.raw=e.raw:e.path?i.content={path:e.path}:e.href?i.content={href:e.href,httpHeaders:e.httpHeaders}:i.content=e.content||"",e.encoding&&(i.encoding=e.encoding),e.headers&&(i.headers=e.headers),i});return(this.mail.icalEvent&&(t="object"==typeof this.mail.icalEvent&&(this.mail.icalEvent.content||this.mail.icalEvent.path||this.mail.icalEvent.href||this.mail.icalEvent.raw)?this.mail.icalEvent:{content:this.mail.icalEvent},i={},Object.keys(t).forEach(e=>{i[e]=t[e]}),i.contentType="application/ics",i.headers||(i.headers={}),i.filename=i.filename||"invite.ics",i.headers["Content-Disposition"]="attachment",i.headers["Content-Transfer-Encoding"]="base64"),e)?{attached:a.filter(e=>!e.cid).concat(i||[]),related:a.filter(e=>!!e.cid)}:{attached:a.concat(i||[]),related:[]}}getAlternatives(){let e=[],t,i,a,n,o,r;return this.mail.text&&((t="object"==typeof this.mail.text&&(this.mail.text.content||this.mail.text.path||this.mail.text.href||this.mail.text.raw)?this.mail.text:{content:this.mail.text}).contentType="text/plain; charset=utf-8"),this.mail.watchHtml&&((a="object"==typeof this.mail.watchHtml&&(this.mail.watchHtml.content||this.mail.watchHtml.path||this.mail.watchHtml.href||this.mail.watchHtml.raw)?this.mail.watchHtml:{content:this.mail.watchHtml}).contentType="text/watch-html; charset=utf-8"),this.mail.amp&&((n="object"==typeof this.mail.amp&&(this.mail.amp.content||this.mail.amp.path||this.mail.amp.href||this.mail.amp.raw)?this.mail.amp:{content:this.mail.amp}).contentType="text/x-amp-html; charset=utf-8"),this.mail.icalEvent&&(o="object"==typeof this.mail.icalEvent&&(this.mail.icalEvent.content||this.mail.icalEvent.path||this.mail.icalEvent.href||this.mail.icalEvent.raw)?this.mail.icalEvent:{content:this.mail.icalEvent},r={},Object.keys(o).forEach(e=>{r[e]=o[e]}),r.content&&"object"==typeof r.content&&(r.content._resolve=!0),r.filename=!1,r.contentType="text/calendar; charset=utf-8; method="+(r.method||"PUBLISH").toString().trim().toUpperCase(),r.headers||(r.headers={})),this.mail.html&&((i="object"==typeof this.mail.html&&(this.mail.html.content||this.mail.html.path||this.mail.html.href||this.mail.html.raw)?this.mail.html:{content:this.mail.html}).contentType="text/html; charset=utf-8"),[].concat(t||[]).concat(a||[]).concat(n||[]).concat(i||[]).concat(r||[]).concat(this.mail.alternatives||[]).forEach(t=>{let i;/^data:/i.test(t.path||t.href)&&(t=this._processDataUrl(t)),i={contentType:t.contentType||s.detectMimeType(t.filename||t.path||t.href||"txt"),contentTransferEncoding:t.contentTransferEncoding},t.filename&&(i.filename=t.filename),/^https?:\/\//i.test(t.path)&&(t.href=t.path,t.path=void 0),t.raw?i.raw=t.raw:t.path?i.content={path:t.path}:t.href?i.content={href:t.href}:i.content=t.content||"",t.encoding&&(i.encoding=t.encoding),t.headers&&(i.headers=t.headers),e.push(i)}),e}_createMixed(e){let t;return t=e?e.createChild("multipart/mixed",{disableUrlAccess:this.mail.disableUrlAccess,disableFileAccess:this.mail.disableFileAccess,normalizeHeaderKey:this.mail.normalizeHeaderKey,newline:this.mail.newline}):new a("multipart/mixed",{baseBoundary:this.mail.baseBoundary,textEncoding:this.mail.textEncoding,boundaryPrefix:this.mail.boundaryPrefix,disableUrlAccess:this.mail.disableUrlAccess,disableFileAccess:this.mail.disableFileAccess,normalizeHeaderKey:this.mail.normalizeHeaderKey,newline:this.mail.newline}),this._useAlternative?this._createAlternative(t):this._useRelated&&this._createRelated(t),[].concat(!this._useAlternative&&this._alternatives||[]).concat(this._attachments.attached||[]).forEach(e=>{this._useRelated&&e===this._htmlNode||this._createContentNode(t,e)}),t}_createAlternative(e){let t;return t=e?e.createChild("multipart/alternative",{disableUrlAccess:this.mail.disableUrlAccess,disableFileAccess:this.mail.disableFileAccess,normalizeHeaderKey:this.mail.normalizeHeaderKey,newline:this.mail.newline}):new a("multipart/alternative",{baseBoundary:this.mail.baseBoundary,textEncoding:this.mail.textEncoding,boundaryPrefix:this.mail.boundaryPrefix,disableUrlAccess:this.mail.disableUrlAccess,disableFileAccess:this.mail.disableFileAccess,normalizeHeaderKey:this.mail.normalizeHeaderKey,newline:this.mail.newline}),this._alternatives.forEach(e=>{this._useRelated&&this._htmlNode===e?this._createRelated(t):this._createContentNode(t,e)}),t}_createRelated(e){let t;return t=e?e.createChild('multipart/related; type="text/html"',{disableUrlAccess:this.mail.disableUrlAccess,disableFileAccess:this.mail.disableFileAccess,normalizeHeaderKey:this.mail.normalizeHeaderKey,newline:this.mail.newline}):new a('multipart/related; type="text/html"',{baseBoundary:this.mail.baseBoundary,textEncoding:this.mail.textEncoding,boundaryPrefix:this.mail.boundaryPrefix,disableUrlAccess:this.mail.disableUrlAccess,disableFileAccess:this.mail.disableFileAccess,normalizeHeaderKey:this.mail.normalizeHeaderKey,newline:this.mail.newline}),this._createContentNode(t,this._htmlNode),this._attachments.related.forEach(e=>this._createContentNode(t,e)),t}_createContentNode(e,t){let i;(t=t||{}).content=t.content||"";let s=(t.encoding||"utf8").toString().toLowerCase().replace(/[-_\s]/g,"");return i=e?e.createChild(t.contentType,{filename:t.filename,textEncoding:this.mail.textEncoding,disableUrlAccess:this.mail.disableUrlAccess,disableFileAccess:this.mail.disableFileAccess,normalizeHeaderKey:this.mail.normalizeHeaderKey,newline:this.mail.newline}):new a(t.contentType,{filename:t.filename,baseBoundary:this.mail.baseBoundary,textEncoding:this.mail.textEncoding,boundaryPrefix:this.mail.boundaryPrefix,disableUrlAccess:this.mail.disableUrlAccess,disableFileAccess:this.mail.disableFileAccess,normalizeHeaderKey:this.mail.normalizeHeaderKey,newline:this.mail.newline}),t.headers&&i.addHeader(t.headers),t.cid&&i.setHeader("Content-Id","<"+t.cid.replace(/[<>]/g,"")+">"),t.contentTransferEncoding?i.setHeader("Content-Transfer-Encoding",t.contentTransferEncoding):this.mail.encoding&&/^text\//i.test(t.contentType)&&i.setHeader("Content-Transfer-Encoding",this.mail.encoding),(!/^text\//i.test(t.contentType)||t.contentDisposition)&&i.setHeader("Content-Disposition",t.contentDisposition||(t.cid&&/^image\//i.test(t.contentType)?"inline":"attachment")),"string"!=typeof t.content||["utf8","usascii","ascii"].includes(s)||(t.content=Buffer.from(t.content,s)),t.raw?i.setRaw(t.raw):i.setContent(t.content),i}_processDataUrl(e){let t;return(e.path||e.href).match(/^data:/)&&(t=n(e.path||e.href)),t&&(e.content=t.data,e.contentType=e.contentType||t.contentType,"path"in e&&(e.path=!1),"href"in e&&(e.href=!1)),e}}e.exports=o},49074:e=>{"use strict";e.exports=JSON.parse('{"name":"nodemailer","version":"6.10.1","description":"Easy as cake e-mail sending from your Node.js applications","main":"lib/nodemailer.js","scripts":{"test":"node --test --test-concurrency=1 test/**/*.test.js test/**/*-test.js","test:coverage":"c8 node --test --test-concurrency=1 test/**/*.test.js test/**/*-test.js","lint":"eslint .","update":"rm -rf node_modules/ package-lock.json && ncu -u && npm install"},"repository":{"type":"git","url":"https://github.com/nodemailer/nodemailer.git"},"keywords":["Nodemailer"],"author":"Andris Reinman","license":"MIT-0","bugs":{"url":"https://github.com/nodemailer/nodemailer/issues"},"homepage":"https://nodemailer.com/","devDependencies":{"@aws-sdk/client-ses":"3.731.1","bunyan":"1.8.15","c8":"10.1.3","eslint":"8.57.0","eslint-config-nodemailer":"1.2.0","eslint-config-prettier":"9.1.0","libbase64":"1.3.0","libmime":"5.3.6","libqp":"2.1.1","nodemailer-ntlm-auth":"1.0.4","proxy":"1.0.2","proxy-test-server":"1.0.0","smtp-server":"3.13.6"},"engines":{"node":">=6.0.0"}}')},49526:(e,t,i)=>{"use strict";let a=i(46945),s=i(91292),n=i(8346),o=i(84681),r=i(79524),p=i(77821),c=i(89767),l=i(97002),d=i(38099),m=i(49074),h=(process.env.ETHEREAL_API||"https://api.nodemailer.com").replace(/\/+$/,""),u=(process.env.ETHEREAL_WEB||"https://ethereal.email").replace(/\/+$/,""),f=(process.env.ETHEREAL_API_KEY||"").replace(/\s*/g,"")||null,x=["true","yes","y","1"].includes((process.env.ETHEREAL_CACHE||"yes").toString().trim().toLowerCase()),g=!1;e.exports.createTransport=function(e,t){let i,d;return("object"==typeof e&&"function"!=typeof e.send||"string"==typeof e&&/^(smtps?|direct):/i.test(e))&&(e=(d=(i="string"==typeof e?e:e.url)?s.parseConnectionUrl(i):e).pool?new n(d):d.sendmail?new r(d):d.streamTransport?new p(d):d.jsonTransport?new c(d):d.SES?new l(d):new o(d)),new a(e,d,t)},e.exports.createTestAccount=function(e,t){let i;if(t||"function"!=typeof e||(t=e,e=!1),t||(i=new Promise((e,i)=>{t=s.callbackPromise(e,i)})),x&&g)return setImmediate(()=>t(null,g)),i;e=e||h;let a=[],n=0,o={},r={requestor:m.name,version:m.version};f&&(o.Authorization="Bearer "+f);let p=d(e+"/user",{contentType:"application/json",method:"POST",headers:o,body:Buffer.from(JSON.stringify(r))});return p.on("readable",()=>{let e;for(;null!==(e=p.read());)a.push(e),n+=e.length}),p.once("error",e=>t(e)),p.once("end",()=>{let e,i,s=Buffer.concat(a,n);try{e=JSON.parse(s.toString())}catch(e){i=e}return i?t(i):"success"!==e.status||e.error?t(Error(e.error||"Request failed")):void(delete e.status,t(null,g=e))}),i},e.exports.getTestMessageUrl=function(e){if(!e||!e.response)return!1;let t=new Map;return e.response.replace(/\[([^\]]+)\]$/,(e,i)=>{i.replace(/\b([A-Z0-9]+)=([^\s]+)/g,(e,i,a)=>{t.set(i,a)})}),!!(t.has("STATUS")&&t.has("MSGID"))&&(g.web||u)+"/message/"+t.get("MSGID")}},50143:(e,t,i)=>{"use strict";let a=i(33873),s="application/octet-stream",n=new Map([["application/acad","dwg"],["application/applixware","aw"],["application/arj","arj"],["application/atom+xml","xml"],["application/atomcat+xml","atomcat"],["application/atomsvc+xml","atomsvc"],["application/base64",["mm","mme"]],["application/binhex","hqx"],["application/binhex4","hqx"],["application/book",["book","boo"]],["application/ccxml+xml,","ccxml"],["application/cdf","cdf"],["application/cdmi-capability","cdmia"],["application/cdmi-container","cdmic"],["application/cdmi-domain","cdmid"],["application/cdmi-object","cdmio"],["application/cdmi-queue","cdmiq"],["application/clariscad","ccad"],["application/commonground","dp"],["application/cu-seeme","cu"],["application/davmount+xml","davmount"],["application/drafting","drw"],["application/dsptype","tsp"],["application/dssc+der","dssc"],["application/dssc+xml","xdssc"],["application/dxf","dxf"],["application/ecmascript",["js","es"]],["application/emma+xml","emma"],["application/envoy","evy"],["application/epub+zip","epub"],["application/excel",["xls","xl","xla","xlb","xlc","xld","xlk","xll","xlm","xlt","xlv","xlw"]],["application/exi","exi"],["application/font-tdpfr","pfr"],["application/fractals","fif"],["application/freeloader","frl"],["application/futuresplash","spl"],["application/geo+json","geojson"],["application/gnutar","tgz"],["application/groupwise","vew"],["application/hlp","hlp"],["application/hta","hta"],["application/hyperstudio","stk"],["application/i-deas","unv"],["application/iges",["iges","igs"]],["application/inf","inf"],["application/internet-property-stream","acx"],["application/ipfix","ipfix"],["application/java","class"],["application/java-archive","jar"],["application/java-byte-code","class"],["application/java-serialized-object","ser"],["application/java-vm","class"],["application/javascript","js"],["application/json","json"],["application/lha","lha"],["application/lzx","lzx"],["application/mac-binary","bin"],["application/mac-binhex","hqx"],["application/mac-binhex40","hqx"],["application/mac-compactpro","cpt"],["application/macbinary","bin"],["application/mads+xml","mads"],["application/marc","mrc"],["application/marcxml+xml","mrcx"],["application/mathematica","ma"],["application/mathml+xml","mathml"],["application/mbedlet","mbd"],["application/mbox","mbox"],["application/mcad","mcd"],["application/mediaservercontrol+xml","mscml"],["application/metalink4+xml","meta4"],["application/mets+xml","mets"],["application/mime","aps"],["application/mods+xml","mods"],["application/mp21","m21"],["application/mp4","mp4"],["application/mspowerpoint",["ppt","pot","pps","ppz"]],["application/msword",["doc","dot","w6w","wiz","word"]],["application/mswrite","wri"],["application/mxf","mxf"],["application/netmc","mcp"],["application/octet-stream",["*"]],["application/oda","oda"],["application/oebps-package+xml","opf"],["application/ogg","ogx"],["application/olescript","axs"],["application/onenote","onetoc"],["application/patch-ops-error+xml","xer"],["application/pdf","pdf"],["application/pgp-encrypted","asc"],["application/pgp-signature","pgp"],["application/pics-rules","prf"],["application/pkcs-12","p12"],["application/pkcs-crl","crl"],["application/pkcs10","p10"],["application/pkcs7-mime",["p7c","p7m"]],["application/pkcs7-signature","p7s"],["application/pkcs8","p8"],["application/pkix-attr-cert","ac"],["application/pkix-cert",["cer","crt"]],["application/pkix-crl","crl"],["application/pkix-pkipath","pkipath"],["application/pkixcmp","pki"],["application/plain","text"],["application/pls+xml","pls"],["application/postscript",["ps","ai","eps"]],["application/powerpoint","ppt"],["application/pro_eng",["part","prt"]],["application/prs.cww","cww"],["application/pskc+xml","pskcxml"],["application/rdf+xml","rdf"],["application/reginfo+xml","rif"],["application/relax-ng-compact-syntax","rnc"],["application/resource-lists+xml","rl"],["application/resource-lists-diff+xml","rld"],["application/ringing-tones","rng"],["application/rls-services+xml","rs"],["application/rsd+xml","rsd"],["application/rss+xml","xml"],["application/rtf",["rtf","rtx"]],["application/sbml+xml","sbml"],["application/scvp-cv-request","scq"],["application/scvp-cv-response","scs"],["application/scvp-vp-request","spq"],["application/scvp-vp-response","spp"],["application/sdp","sdp"],["application/sea","sea"],["application/set","set"],["application/set-payment-initiation","setpay"],["application/set-registration-initiation","setreg"],["application/shf+xml","shf"],["application/sla","stl"],["application/smil",["smi","smil"]],["application/smil+xml","smi"],["application/solids","sol"],["application/sounder","sdr"],["application/sparql-query","rq"],["application/sparql-results+xml","srx"],["application/srgs","gram"],["application/srgs+xml","grxml"],["application/sru+xml","sru"],["application/ssml+xml","ssml"],["application/step",["step","stp"]],["application/streamingmedia","ssm"],["application/tei+xml","tei"],["application/thraud+xml","tfi"],["application/timestamped-data","tsd"],["application/toolbook","tbk"],["application/vda","vda"],["application/vnd.3gpp.pic-bw-large","plb"],["application/vnd.3gpp.pic-bw-small","psb"],["application/vnd.3gpp.pic-bw-var","pvb"],["application/vnd.3gpp2.tcap","tcap"],["application/vnd.3m.post-it-notes","pwn"],["application/vnd.accpac.simply.aso","aso"],["application/vnd.accpac.simply.imp","imp"],["application/vnd.acucobol","acu"],["application/vnd.acucorp","atc"],["application/vnd.adobe.air-application-installer-package+zip","air"],["application/vnd.adobe.fxp","fxp"],["application/vnd.adobe.xdp+xml","xdp"],["application/vnd.adobe.xfdf","xfdf"],["application/vnd.ahead.space","ahead"],["application/vnd.airzip.filesecure.azf","azf"],["application/vnd.airzip.filesecure.azs","azs"],["application/vnd.amazon.ebook","azw"],["application/vnd.americandynamics.acc","acc"],["application/vnd.amiga.ami","ami"],["application/vnd.android.package-archive","apk"],["application/vnd.anser-web-certificate-issue-initiation","cii"],["application/vnd.anser-web-funds-transfer-initiation","fti"],["application/vnd.antix.game-component","atx"],["application/vnd.apple.installer+xml","mpkg"],["application/vnd.apple.mpegurl","m3u8"],["application/vnd.aristanetworks.swi","swi"],["application/vnd.audiograph","aep"],["application/vnd.blueice.multipass","mpm"],["application/vnd.bmi","bmi"],["application/vnd.businessobjects","rep"],["application/vnd.chemdraw+xml","cdxml"],["application/vnd.chipnuts.karaoke-mmd","mmd"],["application/vnd.cinderella","cdy"],["application/vnd.claymore","cla"],["application/vnd.cloanto.rp9","rp9"],["application/vnd.clonk.c4group","c4g"],["application/vnd.cluetrust.cartomobile-config","c11amc"],["application/vnd.cluetrust.cartomobile-config-pkg","c11amz"],["application/vnd.commonspace","csp"],["application/vnd.contact.cmsg","cdbcmsg"],["application/vnd.cosmocaller","cmc"],["application/vnd.crick.clicker","clkx"],["application/vnd.crick.clicker.keyboard","clkk"],["application/vnd.crick.clicker.palette","clkp"],["application/vnd.crick.clicker.template","clkt"],["application/vnd.crick.clicker.wordbank","clkw"],["application/vnd.criticaltools.wbs+xml","wbs"],["application/vnd.ctc-posml","pml"],["application/vnd.cups-ppd","ppd"],["application/vnd.curl.car","car"],["application/vnd.curl.pcurl","pcurl"],["application/vnd.data-vision.rdz","rdz"],["application/vnd.denovo.fcselayout-link","fe_launch"],["application/vnd.dna","dna"],["application/vnd.dolby.mlp","mlp"],["application/vnd.dpgraph","dpg"],["application/vnd.dreamfactory","dfac"],["application/vnd.dvb.ait","ait"],["application/vnd.dvb.service","svc"],["application/vnd.dynageo","geo"],["application/vnd.ecowin.chart","mag"],["application/vnd.enliven","nml"],["application/vnd.epson.esf","esf"],["application/vnd.epson.msf","msf"],["application/vnd.epson.quickanime","qam"],["application/vnd.epson.salt","slt"],["application/vnd.epson.ssf","ssf"],["application/vnd.eszigno3+xml","es3"],["application/vnd.ezpix-album","ez2"],["application/vnd.ezpix-package","ez3"],["application/vnd.fdf","fdf"],["application/vnd.fdsn.seed","seed"],["application/vnd.flographit","gph"],["application/vnd.fluxtime.clip","ftc"],["application/vnd.framemaker","fm"],["application/vnd.frogans.fnc","fnc"],["application/vnd.frogans.ltf","ltf"],["application/vnd.fsc.weblaunch","fsc"],["application/vnd.fujitsu.oasys","oas"],["application/vnd.fujitsu.oasys2","oa2"],["application/vnd.fujitsu.oasys3","oa3"],["application/vnd.fujitsu.oasysgp","fg5"],["application/vnd.fujitsu.oasysprs","bh2"],["application/vnd.fujixerox.ddd","ddd"],["application/vnd.fujixerox.docuworks","xdw"],["application/vnd.fujixerox.docuworks.binder","xbd"],["application/vnd.fuzzysheet","fzs"],["application/vnd.genomatix.tuxedo","txd"],["application/vnd.geogebra.file","ggb"],["application/vnd.geogebra.tool","ggt"],["application/vnd.geometry-explorer","gex"],["application/vnd.geonext","gxt"],["application/vnd.geoplan","g2w"],["application/vnd.geospace","g3w"],["application/vnd.gmx","gmx"],["application/vnd.google-earth.kml+xml","kml"],["application/vnd.google-earth.kmz","kmz"],["application/vnd.grafeq","gqf"],["application/vnd.groove-account","gac"],["application/vnd.groove-help","ghf"],["application/vnd.groove-identity-message","gim"],["application/vnd.groove-injector","grv"],["application/vnd.groove-tool-message","gtm"],["application/vnd.groove-tool-template","tpl"],["application/vnd.groove-vcard","vcg"],["application/vnd.hal+xml","hal"],["application/vnd.handheld-entertainment+xml","zmm"],["application/vnd.hbci","hbci"],["application/vnd.hhe.lesson-player","les"],["application/vnd.hp-hpgl",["hgl","hpg","hpgl"]],["application/vnd.hp-hpid","hpid"],["application/vnd.hp-hps","hps"],["application/vnd.hp-jlyt","jlt"],["application/vnd.hp-pcl","pcl"],["application/vnd.hp-pclxl","pclxl"],["application/vnd.hydrostatix.sof-data","sfd-hdstx"],["application/vnd.hzn-3d-crossword","x3d"],["application/vnd.ibm.minipay","mpy"],["application/vnd.ibm.modcap","afp"],["application/vnd.ibm.rights-management","irm"],["application/vnd.ibm.secure-container","sc"],["application/vnd.iccprofile","icc"],["application/vnd.igloader","igl"],["application/vnd.immervision-ivp","ivp"],["application/vnd.immervision-ivu","ivu"],["application/vnd.insors.igm","igm"],["application/vnd.intercon.formnet","xpw"],["application/vnd.intergeo","i2g"],["application/vnd.intu.qbo","qbo"],["application/vnd.intu.qfx","qfx"],["application/vnd.ipunplugged.rcprofile","rcprofile"],["application/vnd.irepository.package+xml","irp"],["application/vnd.is-xpr","xpr"],["application/vnd.isac.fcs","fcs"],["application/vnd.jam","jam"],["application/vnd.jcp.javame.midlet-rms","rms"],["application/vnd.jisp","jisp"],["application/vnd.joost.joda-archive","joda"],["application/vnd.kahootz","ktz"],["application/vnd.kde.karbon","karbon"],["application/vnd.kde.kchart","chrt"],["application/vnd.kde.kformula","kfo"],["application/vnd.kde.kivio","flw"],["application/vnd.kde.kontour","kon"],["application/vnd.kde.kpresenter","kpr"],["application/vnd.kde.kspread","ksp"],["application/vnd.kde.kword","kwd"],["application/vnd.kenameaapp","htke"],["application/vnd.kidspiration","kia"],["application/vnd.kinar","kne"],["application/vnd.koan","skp"],["application/vnd.kodak-descriptor","sse"],["application/vnd.las.las+xml","lasxml"],["application/vnd.llamagraphics.life-balance.desktop","lbd"],["application/vnd.llamagraphics.life-balance.exchange+xml","lbe"],["application/vnd.lotus-1-2-3","123"],["application/vnd.lotus-approach","apr"],["application/vnd.lotus-freelance","pre"],["application/vnd.lotus-notes","nsf"],["application/vnd.lotus-organizer","org"],["application/vnd.lotus-screencam","scm"],["application/vnd.lotus-wordpro","lwp"],["application/vnd.macports.portpkg","portpkg"],["application/vnd.mcd","mcd"],["application/vnd.medcalcdata","mc1"],["application/vnd.mediastation.cdkey","cdkey"],["application/vnd.mfer","mwf"],["application/vnd.mfmp","mfm"],["application/vnd.micrografx.flo","flo"],["application/vnd.micrografx.igx","igx"],["application/vnd.mif","mif"],["application/vnd.mobius.daf","daf"],["application/vnd.mobius.dis","dis"],["application/vnd.mobius.mbk","mbk"],["application/vnd.mobius.mqy","mqy"],["application/vnd.mobius.msl","msl"],["application/vnd.mobius.plc","plc"],["application/vnd.mobius.txf","txf"],["application/vnd.mophun.application","mpn"],["application/vnd.mophun.certificate","mpc"],["application/vnd.mozilla.xul+xml","xul"],["application/vnd.ms-artgalry","cil"],["application/vnd.ms-cab-compressed","cab"],["application/vnd.ms-excel",["xls","xla","xlc","xlm","xlt","xlw","xlb","xll"]],["application/vnd.ms-excel.addin.macroenabled.12","xlam"],["application/vnd.ms-excel.sheet.binary.macroenabled.12","xlsb"],["application/vnd.ms-excel.sheet.macroenabled.12","xlsm"],["application/vnd.ms-excel.template.macroenabled.12","xltm"],["application/vnd.ms-fontobject","eot"],["application/vnd.ms-htmlhelp","chm"],["application/vnd.ms-ims","ims"],["application/vnd.ms-lrm","lrm"],["application/vnd.ms-officetheme","thmx"],["application/vnd.ms-outlook","msg"],["application/vnd.ms-pki.certstore","sst"],["application/vnd.ms-pki.pko","pko"],["application/vnd.ms-pki.seccat","cat"],["application/vnd.ms-pki.stl","stl"],["application/vnd.ms-pkicertstore","sst"],["application/vnd.ms-pkiseccat","cat"],["application/vnd.ms-pkistl","stl"],["application/vnd.ms-powerpoint",["ppt","pot","pps","ppa","pwz"]],["application/vnd.ms-powerpoint.addin.macroenabled.12","ppam"],["application/vnd.ms-powerpoint.presentation.macroenabled.12","pptm"],["application/vnd.ms-powerpoint.slide.macroenabled.12","sldm"],["application/vnd.ms-powerpoint.slideshow.macroenabled.12","ppsm"],["application/vnd.ms-powerpoint.template.macroenabled.12","potm"],["application/vnd.ms-project","mpp"],["application/vnd.ms-word.document.macroenabled.12","docm"],["application/vnd.ms-word.template.macroenabled.12","dotm"],["application/vnd.ms-works",["wks","wcm","wdb","wps"]],["application/vnd.ms-wpl","wpl"],["application/vnd.ms-xpsdocument","xps"],["application/vnd.mseq","mseq"],["application/vnd.musician","mus"],["application/vnd.muvee.style","msty"],["application/vnd.neurolanguage.nlu","nlu"],["application/vnd.noblenet-directory","nnd"],["application/vnd.noblenet-sealer","nns"],["application/vnd.noblenet-web","nnw"],["application/vnd.nokia.configuration-message","ncm"],["application/vnd.nokia.n-gage.data","ngdat"],["application/vnd.nokia.n-gage.symbian.install","n-gage"],["application/vnd.nokia.radio-preset","rpst"],["application/vnd.nokia.radio-presets","rpss"],["application/vnd.nokia.ringing-tone","rng"],["application/vnd.novadigm.edm","edm"],["application/vnd.novadigm.edx","edx"],["application/vnd.novadigm.ext","ext"],["application/vnd.oasis.opendocument.chart","odc"],["application/vnd.oasis.opendocument.chart-template","otc"],["application/vnd.oasis.opendocument.database","odb"],["application/vnd.oasis.opendocument.formula","odf"],["application/vnd.oasis.opendocument.formula-template","odft"],["application/vnd.oasis.opendocument.graphics","odg"],["application/vnd.oasis.opendocument.graphics-template","otg"],["application/vnd.oasis.opendocument.image","odi"],["application/vnd.oasis.opendocument.image-template","oti"],["application/vnd.oasis.opendocument.presentation","odp"],["application/vnd.oasis.opendocument.presentation-template","otp"],["application/vnd.oasis.opendocument.spreadsheet","ods"],["application/vnd.oasis.opendocument.spreadsheet-template","ots"],["application/vnd.oasis.opendocument.text","odt"],["application/vnd.oasis.opendocument.text-master","odm"],["application/vnd.oasis.opendocument.text-template","ott"],["application/vnd.oasis.opendocument.text-web","oth"],["application/vnd.olpc-sugar","xo"],["application/vnd.oma.dd2+xml","dd2"],["application/vnd.openofficeorg.extension","oxt"],["application/vnd.openxmlformats-officedocument.presentationml.presentation","pptx"],["application/vnd.openxmlformats-officedocument.presentationml.slide","sldx"],["application/vnd.openxmlformats-officedocument.presentationml.slideshow","ppsx"],["application/vnd.openxmlformats-officedocument.presentationml.template","potx"],["application/vnd.openxmlformats-officedocument.spreadsheetml.sheet","xlsx"],["application/vnd.openxmlformats-officedocument.spreadsheetml.template","xltx"],["application/vnd.openxmlformats-officedocument.wordprocessingml.document","docx"],["application/vnd.openxmlformats-officedocument.wordprocessingml.template","dotx"],["application/vnd.osgeo.mapguide.package","mgp"],["application/vnd.osgi.dp","dp"],["application/vnd.palm","pdb"],["application/vnd.pawaafile","paw"],["application/vnd.pg.format","str"],["application/vnd.pg.osasli","ei6"],["application/vnd.picsel","efif"],["application/vnd.pmi.widget","wg"],["application/vnd.pocketlearn","plf"],["application/vnd.powerbuilder6","pbd"],["application/vnd.previewsystems.box","box"],["application/vnd.proteus.magazine","mgz"],["application/vnd.publishare-delta-tree","qps"],["application/vnd.pvi.ptid1","ptid"],["application/vnd.quark.quarkxpress","qxd"],["application/vnd.realvnc.bed","bed"],["application/vnd.recordare.musicxml","mxl"],["application/vnd.recordare.musicxml+xml","musicxml"],["application/vnd.rig.cryptonote","cryptonote"],["application/vnd.rim.cod","cod"],["application/vnd.rn-realmedia","rm"],["application/vnd.rn-realplayer","rnx"],["application/vnd.route66.link66+xml","link66"],["application/vnd.sailingtracker.track","st"],["application/vnd.seemail","see"],["application/vnd.sema","sema"],["application/vnd.semd","semd"],["application/vnd.semf","semf"],["application/vnd.shana.informed.formdata","ifm"],["application/vnd.shana.informed.formtemplate","itp"],["application/vnd.shana.informed.interchange","iif"],["application/vnd.shana.informed.package","ipk"],["application/vnd.simtech-mindmapper","twd"],["application/vnd.smaf","mmf"],["application/vnd.smart.teacher","teacher"],["application/vnd.solent.sdkm+xml","sdkm"],["application/vnd.spotfire.dxp","dxp"],["application/vnd.spotfire.sfs","sfs"],["application/vnd.stardivision.calc","sdc"],["application/vnd.stardivision.draw","sda"],["application/vnd.stardivision.impress","sdd"],["application/vnd.stardivision.math","smf"],["application/vnd.stardivision.writer","sdw"],["application/vnd.stardivision.writer-global","sgl"],["application/vnd.stepmania.stepchart","sm"],["application/vnd.sun.xml.calc","sxc"],["application/vnd.sun.xml.calc.template","stc"],["application/vnd.sun.xml.draw","sxd"],["application/vnd.sun.xml.draw.template","std"],["application/vnd.sun.xml.impress","sxi"],["application/vnd.sun.xml.impress.template","sti"],["application/vnd.sun.xml.math","sxm"],["application/vnd.sun.xml.writer","sxw"],["application/vnd.sun.xml.writer.global","sxg"],["application/vnd.sun.xml.writer.template","stw"],["application/vnd.sus-calendar","sus"],["application/vnd.svd","svd"],["application/vnd.symbian.install","sis"],["application/vnd.syncml+xml","xsm"],["application/vnd.syncml.dm+wbxml","bdm"],["application/vnd.syncml.dm+xml","xdm"],["application/vnd.tao.intent-module-archive","tao"],["application/vnd.tmobile-livetv","tmo"],["application/vnd.trid.tpt","tpt"],["application/vnd.triscape.mxs","mxs"],["application/vnd.trueapp","tra"],["application/vnd.ufdl","ufd"],["application/vnd.uiq.theme","utz"],["application/vnd.umajin","umj"],["application/vnd.unity","unityweb"],["application/vnd.uoml+xml","uoml"],["application/vnd.vcx","vcx"],["application/vnd.visio","vsd"],["application/vnd.visionary","vis"],["application/vnd.vsf","vsf"],["application/vnd.wap.wbxml","wbxml"],["application/vnd.wap.wmlc","wmlc"],["application/vnd.wap.wmlscriptc","wmlsc"],["application/vnd.webturbo","wtb"],["application/vnd.wolfram.player","nbp"],["application/vnd.wordperfect","wpd"],["application/vnd.wqd","wqd"],["application/vnd.wt.stf","stf"],["application/vnd.xara",["web","xar"]],["application/vnd.xfdl","xfdl"],["application/vnd.yamaha.hv-dic","hvd"],["application/vnd.yamaha.hv-script","hvs"],["application/vnd.yamaha.hv-voice","hvp"],["application/vnd.yamaha.openscoreformat","osf"],["application/vnd.yamaha.openscoreformat.osfpvg+xml","osfpvg"],["application/vnd.yamaha.smaf-audio","saf"],["application/vnd.yamaha.smaf-phrase","spf"],["application/vnd.yellowriver-custom-menu","cmp"],["application/vnd.zul","zir"],["application/vnd.zzazz.deck+xml","zaz"],["application/vocaltec-media-desc","vmd"],["application/vocaltec-media-file","vmf"],["application/voicexml+xml","vxml"],["application/widget","wgt"],["application/winhlp","hlp"],["application/wordperfect",["wp","wp5","wp6","wpd"]],["application/wordperfect6.0",["w60","wp5"]],["application/wordperfect6.1","w61"],["application/wsdl+xml","wsdl"],["application/wspolicy+xml","wspolicy"],["application/x-123","wk1"],["application/x-7z-compressed","7z"],["application/x-abiword","abw"],["application/x-ace-compressed","ace"],["application/x-aim","aim"],["application/x-authorware-bin","aab"],["application/x-authorware-map","aam"],["application/x-authorware-seg","aas"],["application/x-bcpio","bcpio"],["application/x-binary","bin"],["application/x-binhex40","hqx"],["application/x-bittorrent","torrent"],["application/x-bsh",["bsh","sh","shar"]],["application/x-bytecode.elisp","elc"],["application/x-bytecode.python","pyc"],["application/x-bzip","bz"],["application/x-bzip2",["boz","bz2"]],["application/x-cdf","cdf"],["application/x-cdlink","vcd"],["application/x-chat",["cha","chat"]],["application/x-chess-pgn","pgn"],["application/x-cmu-raster","ras"],["application/x-cocoa","cco"],["application/x-compactpro","cpt"],["application/x-compress","z"],["application/x-compressed",["tgz","gz","z","zip"]],["application/x-conference","nsc"],["application/x-cpio","cpio"],["application/x-cpt","cpt"],["application/x-csh","csh"],["application/x-debian-package","deb"],["application/x-deepv","deepv"],["application/x-director",["dir","dcr","dxr"]],["application/x-doom","wad"],["application/x-dtbncx+xml","ncx"],["application/x-dtbook+xml","dtb"],["application/x-dtbresource+xml","res"],["application/x-dvi","dvi"],["application/x-elc","elc"],["application/x-envoy",["env","evy"]],["application/x-esrehber","es"],["application/x-excel",["xls","xla","xlb","xlc","xld","xlk","xll","xlm","xlt","xlv","xlw"]],["application/x-font-bdf","bdf"],["application/x-font-ghostscript","gsf"],["application/x-font-linux-psf","psf"],["application/x-font-otf","otf"],["application/x-font-pcf","pcf"],["application/x-font-snf","snf"],["application/x-font-ttf","ttf"],["application/x-font-type1","pfa"],["application/x-font-woff","woff"],["application/x-frame","mif"],["application/x-freelance","pre"],["application/x-futuresplash","spl"],["application/x-gnumeric","gnumeric"],["application/x-gsp","gsp"],["application/x-gss","gss"],["application/x-gtar","gtar"],["application/x-gzip",["gz","gzip"]],["application/x-hdf","hdf"],["application/x-helpfile",["help","hlp"]],["application/x-httpd-imap","imap"],["application/x-ima","ima"],["application/x-internet-signup",["ins","isp"]],["application/x-internett-signup","ins"],["application/x-inventor","iv"],["application/x-ip2","ip"],["application/x-iphone","iii"],["application/x-java-class","class"],["application/x-java-commerce","jcm"],["application/x-java-jnlp-file","jnlp"],["application/x-javascript","js"],["application/x-koan",["skd","skm","skp","skt"]],["application/x-ksh","ksh"],["application/x-latex",["latex","ltx"]],["application/x-lha","lha"],["application/x-lisp","lsp"],["application/x-livescreen","ivy"],["application/x-lotus","wq1"],["application/x-lotusscreencam","scm"],["application/x-lzh","lzh"],["application/x-lzx","lzx"],["application/x-mac-binhex40","hqx"],["application/x-macbinary","bin"],["application/x-magic-cap-package-1.0","mc$"],["application/x-mathcad","mcd"],["application/x-meme","mm"],["application/x-midi",["mid","midi"]],["application/x-mif","mif"],["application/x-mix-transfer","nix"],["application/x-mobipocket-ebook","prc"],["application/x-mplayer2","asx"],["application/x-ms-application","application"],["application/x-ms-wmd","wmd"],["application/x-ms-wmz","wmz"],["application/x-ms-xbap","xbap"],["application/x-msaccess","mdb"],["application/x-msbinder","obd"],["application/x-mscardfile","crd"],["application/x-msclip","clp"],["application/x-msdownload",["exe","dll"]],["application/x-msexcel",["xls","xla","xlw"]],["application/x-msmediaview",["mvb","m13","m14"]],["application/x-msmetafile","wmf"],["application/x-msmoney","mny"],["application/x-mspowerpoint","ppt"],["application/x-mspublisher","pub"],["application/x-msschedule","scd"],["application/x-msterminal","trm"],["application/x-mswrite","wri"],["application/x-navi-animation","ani"],["application/x-navidoc","nvd"],["application/x-navimap","map"],["application/x-navistyle","stl"],["application/x-netcdf",["cdf","nc"]],["application/x-newton-compatible-pkg","pkg"],["application/x-nokia-9000-communicator-add-on-software","aos"],["application/x-omc","omc"],["application/x-omcdatamaker","omcd"],["application/x-omcregerator","omcr"],["application/x-pagemaker",["pm4","pm5"]],["application/x-pcl","pcl"],["application/x-perfmon",["pma","pmc","pml","pmr","pmw"]],["application/x-pixclscript","plx"],["application/x-pkcs10","p10"],["application/x-pkcs12",["p12","pfx"]],["application/x-pkcs7-certificates",["p7b","spc"]],["application/x-pkcs7-certreqresp","p7r"],["application/x-pkcs7-mime",["p7m","p7c"]],["application/x-pkcs7-signature",["p7s","p7a"]],["application/x-pointplus","css"],["application/x-portable-anymap","pnm"],["application/x-project",["mpc","mpt","mpv","mpx"]],["application/x-qpro","wb1"],["application/x-rar-compressed","rar"],["application/x-rtf","rtf"],["application/x-sdp","sdp"],["application/x-sea","sea"],["application/x-seelogo","sl"],["application/x-sh","sh"],["application/x-shar",["shar","sh"]],["application/x-shockwave-flash","swf"],["application/x-silverlight-app","xap"],["application/x-sit","sit"],["application/x-sprite",["spr","sprite"]],["application/x-stuffit","sit"],["application/x-stuffitx","sitx"],["application/x-sv4cpio","sv4cpio"],["application/x-sv4crc","sv4crc"],["application/x-tar","tar"],["application/x-tbook",["sbk","tbk"]],["application/x-tcl","tcl"],["application/x-tex","tex"],["application/x-tex-tfm","tfm"],["application/x-texinfo",["texi","texinfo"]],["application/x-troff",["roff","t","tr"]],["application/x-troff-man","man"],["application/x-troff-me","me"],["application/x-troff-ms","ms"],["application/x-troff-msvideo","avi"],["application/x-ustar","ustar"],["application/x-visio",["vsd","vst","vsw"]],["application/x-vnd.audioexplosion.mzz","mzz"],["application/x-vnd.ls-xpix","xpix"],["application/x-vrml","vrml"],["application/x-wais-source",["src","wsrc"]],["application/x-winhelp","hlp"],["application/x-wintalk","wtk"],["application/x-world",["wrl","svr"]],["application/x-wpwin","wpd"],["application/x-wri","wri"],["application/x-x509-ca-cert",["cer","crt","der"]],["application/x-x509-user-cert","crt"],["application/x-xfig","fig"],["application/x-xpinstall","xpi"],["application/x-zip-compressed","zip"],["application/xcap-diff+xml","xdf"],["application/xenc+xml","xenc"],["application/xhtml+xml","xhtml"],["application/xml","xml"],["application/xml-dtd","dtd"],["application/xop+xml","xop"],["application/xslt+xml","xslt"],["application/xspf+xml","xspf"],["application/xv+xml","mxml"],["application/yang","yang"],["application/yin+xml","yin"],["application/ynd.ms-pkipko","pko"],["application/zip","zip"],["audio/adpcm","adp"],["audio/aiff",["aiff","aif","aifc"]],["audio/basic",["snd","au"]],["audio/it","it"],["audio/make",["funk","my","pfunk"]],["audio/make.my.funk","pfunk"],["audio/mid",["mid","rmi"]],["audio/midi",["midi","kar","mid"]],["audio/mod","mod"],["audio/mp4","mp4a"],["audio/mpeg",["mpga","mp3","m2a","mp2","mpa","mpg"]],["audio/mpeg3","mp3"],["audio/nspaudio",["la","lma"]],["audio/ogg","oga"],["audio/s3m","s3m"],["audio/tsp-audio","tsi"],["audio/tsplayer","tsp"],["audio/vnd.dece.audio","uva"],["audio/vnd.digital-winds","eol"],["audio/vnd.dra","dra"],["audio/vnd.dts","dts"],["audio/vnd.dts.hd","dtshd"],["audio/vnd.lucent.voice","lvp"],["audio/vnd.ms-playready.media.pya","pya"],["audio/vnd.nuera.ecelp4800","ecelp4800"],["audio/vnd.nuera.ecelp7470","ecelp7470"],["audio/vnd.nuera.ecelp9600","ecelp9600"],["audio/vnd.qcelp","qcp"],["audio/vnd.rip","rip"],["audio/voc","voc"],["audio/voxware","vox"],["audio/wav","wav"],["audio/webm","weba"],["audio/x-aac","aac"],["audio/x-adpcm","snd"],["audio/x-aiff",["aiff","aif","aifc"]],["audio/x-au","au"],["audio/x-gsm",["gsd","gsm"]],["audio/x-jam","jam"],["audio/x-liveaudio","lam"],["audio/x-mid",["mid","midi"]],["audio/x-midi",["midi","mid"]],["audio/x-mod","mod"],["audio/x-mpeg","mp2"],["audio/x-mpeg-3","mp3"],["audio/x-mpegurl","m3u"],["audio/x-mpequrl","m3u"],["audio/x-ms-wax","wax"],["audio/x-ms-wma","wma"],["audio/x-nspaudio",["la","lma"]],["audio/x-pn-realaudio",["ra","ram","rm","rmm","rmp"]],["audio/x-pn-realaudio-plugin",["ra","rmp","rpm"]],["audio/x-psid","sid"],["audio/x-realaudio","ra"],["audio/x-twinvq","vqf"],["audio/x-twinvq-plugin",["vqe","vql"]],["audio/x-vnd.audioexplosion.mjuicemediafile","mjf"],["audio/x-voc","voc"],["audio/x-wav","wav"],["audio/xm","xm"],["chemical/x-cdx","cdx"],["chemical/x-cif","cif"],["chemical/x-cmdf","cmdf"],["chemical/x-cml","cml"],["chemical/x-csml","csml"],["chemical/x-pdb",["pdb","xyz"]],["chemical/x-xyz","xyz"],["drawing/x-dwf","dwf"],["i-world/i-vrml","ivr"],["image/bmp",["bmp","bm"]],["image/cgm","cgm"],["image/cis-cod","cod"],["image/cmu-raster",["ras","rast"]],["image/fif","fif"],["image/florian",["flo","turbot"]],["image/g3fax","g3"],["image/gif","gif"],["image/ief",["ief","iefs"]],["image/jpeg",["jpeg","jpe","jpg","jfif","jfif-tbnl"]],["image/jutvision","jut"],["image/ktx","ktx"],["image/naplps",["nap","naplps"]],["image/pict",["pic","pict"]],["image/pipeg","jfif"],["image/pjpeg",["jfif","jpe","jpeg","jpg"]],["image/png",["png","x-png"]],["image/prs.btif","btif"],["image/svg+xml","svg"],["image/tiff",["tif","tiff"]],["image/vasa","mcf"],["image/vnd.adobe.photoshop","psd"],["image/vnd.dece.graphic","uvi"],["image/vnd.djvu","djvu"],["image/vnd.dvb.subtitle","sub"],["image/vnd.dwg",["dwg","dxf","svf"]],["image/vnd.dxf","dxf"],["image/vnd.fastbidsheet","fbs"],["image/vnd.fpx","fpx"],["image/vnd.fst","fst"],["image/vnd.fujixerox.edmics-mmr","mmr"],["image/vnd.fujixerox.edmics-rlc","rlc"],["image/vnd.ms-modi","mdi"],["image/vnd.net-fpx",["fpx","npx"]],["image/vnd.rn-realflash","rf"],["image/vnd.rn-realpix","rp"],["image/vnd.wap.wbmp","wbmp"],["image/vnd.xiff","xif"],["image/webp","webp"],["image/x-cmu-raster","ras"],["image/x-cmx","cmx"],["image/x-dwg",["dwg","dxf","svf"]],["image/x-freehand","fh"],["image/x-icon","ico"],["image/x-jg","art"],["image/x-jps","jps"],["image/x-niff",["niff","nif"]],["image/x-pcx","pcx"],["image/x-pict",["pct","pic"]],["image/x-portable-anymap","pnm"],["image/x-portable-bitmap","pbm"],["image/x-portable-graymap","pgm"],["image/x-portable-greymap","pgm"],["image/x-portable-pixmap","ppm"],["image/x-quicktime",["qif","qti","qtif"]],["image/x-rgb","rgb"],["image/x-tiff",["tif","tiff"]],["image/x-windows-bmp","bmp"],["image/x-xbitmap","xbm"],["image/x-xbm","xbm"],["image/x-xpixmap",["xpm","pm"]],["image/x-xwd","xwd"],["image/x-xwindowdump","xwd"],["image/xbm","xbm"],["image/xpm","xpm"],["message/rfc822",["eml","mht","mhtml","nws","mime"]],["model/iges",["iges","igs"]],["model/mesh","msh"],["model/vnd.collada+xml","dae"],["model/vnd.dwf","dwf"],["model/vnd.gdl","gdl"],["model/vnd.gtw","gtw"],["model/vnd.mts","mts"],["model/vnd.vtu","vtu"],["model/vrml",["vrml","wrl","wrz"]],["model/x-pov","pov"],["multipart/x-gzip","gzip"],["multipart/x-ustar","ustar"],["multipart/x-zip","zip"],["music/crescendo",["mid","midi"]],["music/x-karaoke","kar"],["paleovu/x-pv","pvu"],["text/asp","asp"],["text/calendar","ics"],["text/css","css"],["text/csv","csv"],["text/ecmascript","js"],["text/h323","323"],["text/html",["html","htm","stm","acgi","htmls","htx","shtml"]],["text/iuls","uls"],["text/javascript","js"],["text/mcf","mcf"],["text/n3","n3"],["text/pascal","pas"],["text/plain",["txt","bas","c","h","c++","cc","com","conf","cxx","def","f","f90","for","g","hh","idc","jav","java","list","log","lst","m","mar","pl","sdml","text"]],["text/plain-bas","par"],["text/prs.lines.tag","dsc"],["text/richtext",["rtx","rt","rtf"]],["text/scriplet","wsc"],["text/scriptlet","sct"],["text/sgml",["sgm","sgml"]],["text/tab-separated-values","tsv"],["text/troff","t"],["text/turtle","ttl"],["text/uri-list",["uni","unis","uri","uris"]],["text/vnd.abc","abc"],["text/vnd.curl","curl"],["text/vnd.curl.dcurl","dcurl"],["text/vnd.curl.mcurl","mcurl"],["text/vnd.curl.scurl","scurl"],["text/vnd.fly","fly"],["text/vnd.fmi.flexstor","flx"],["text/vnd.graphviz","gv"],["text/vnd.in3d.3dml","3dml"],["text/vnd.in3d.spot","spot"],["text/vnd.rn-realtext","rt"],["text/vnd.sun.j2me.app-descriptor","jad"],["text/vnd.wap.wml","wml"],["text/vnd.wap.wmlscript","wmls"],["text/webviewhtml","htt"],["text/x-asm",["asm","s"]],["text/x-audiosoft-intra","aip"],["text/x-c",["c","cc","cpp"]],["text/x-component","htc"],["text/x-fortran",["for","f","f77","f90"]],["text/x-h",["h","hh"]],["text/x-java-source",["java","jav"]],["text/x-java-source,java","java"],["text/x-la-asf","lsx"],["text/x-m","m"],["text/x-pascal","p"],["text/x-script","hlb"],["text/x-script.csh","csh"],["text/x-script.elisp","el"],["text/x-script.guile","scm"],["text/x-script.ksh","ksh"],["text/x-script.lisp","lsp"],["text/x-script.perl","pl"],["text/x-script.perl-module","pm"],["text/x-script.phyton","py"],["text/x-script.rexx","rexx"],["text/x-script.scheme","scm"],["text/x-script.sh","sh"],["text/x-script.tcl","tcl"],["text/x-script.tcsh","tcsh"],["text/x-script.zsh","zsh"],["text/x-server-parsed-html",["shtml","ssi"]],["text/x-setext","etx"],["text/x-sgml",["sgm","sgml"]],["text/x-speech",["spc","talk"]],["text/x-uil","uil"],["text/x-uuencode",["uu","uue"]],["text/x-vcalendar","vcs"],["text/x-vcard","vcf"],["text/xml","xml"],["video/3gpp","3gp"],["video/3gpp2","3g2"],["video/animaflex","afl"],["video/avi","avi"],["video/avs-video","avs"],["video/dl","dl"],["video/fli","fli"],["video/gl","gl"],["video/h261","h261"],["video/h263","h263"],["video/h264","h264"],["video/jpeg","jpgv"],["video/jpm","jpm"],["video/mj2","mj2"],["video/mp4","mp4"],["video/mpeg",["mpeg","mp2","mpa","mpe","mpg","mpv2","m1v","m2v","mp3"]],["video/msvideo","avi"],["video/ogg","ogv"],["video/quicktime",["mov","qt","moov"]],["video/vdo","vdo"],["video/vivo",["viv","vivo"]],["video/vnd.dece.hd","uvh"],["video/vnd.dece.mobile","uvm"],["video/vnd.dece.pd","uvp"],["video/vnd.dece.sd","uvs"],["video/vnd.dece.video","uvv"],["video/vnd.fvt","fvt"],["video/vnd.mpegurl","mxu"],["video/vnd.ms-playready.media.pyv","pyv"],["video/vnd.rn-realvideo","rv"],["video/vnd.uvvu.mp4","uvu"],["video/vnd.vivo",["viv","vivo"]],["video/vosaic","vos"],["video/webm","webm"],["video/x-amt-demorun","xdr"],["video/x-amt-showrun","xsr"],["video/x-atomic3d-feature","fmf"],["video/x-dl","dl"],["video/x-dv",["dif","dv"]],["video/x-f4v","f4v"],["video/x-fli","fli"],["video/x-flv","flv"],["video/x-gl","gl"],["video/x-isvideo","isu"],["video/x-la-asf",["lsf","lsx"]],["video/x-m4v","m4v"],["video/x-motion-jpeg","mjpg"],["video/x-mpeg",["mp3","mp2"]],["video/x-mpeq2a","mp2"],["video/x-ms-asf",["asf","asr","asx"]],["video/x-ms-asf-plugin","asx"],["video/x-ms-wm","wm"],["video/x-ms-wmv","wmv"],["video/x-ms-wmx","wmx"],["video/x-ms-wvx","wvx"],["video/x-msvideo","avi"],["video/x-qtc","qtc"],["video/x-scm","scm"],["video/x-sgi-movie",["movie","mv"]],["windows/metafile","wmf"],["www/mime","mime"],["x-conference/x-cooltalk","ice"],["x-music/x-midi",["mid","midi"]],["x-world/x-3dmf",["3dm","3dmf","qd3","qd3d"]],["x-world/x-svr","svr"],["x-world/x-vrml",["flr","vrml","wrl","wrz","xaf","xof"]],["x-world/x-vrt","vrt"],["xgl/drawing","xgz"],["xgl/movie","xmz"]]),o=new Map([["123","application/vnd.lotus-1-2-3"],["323","text/h323"],["*","application/octet-stream"],["3dm","x-world/x-3dmf"],["3dmf","x-world/x-3dmf"],["3dml","text/vnd.in3d.3dml"],["3g2","video/3gpp2"],["3gp","video/3gpp"],["7z","application/x-7z-compressed"],["a","application/octet-stream"],["aab","application/x-authorware-bin"],["aac","audio/x-aac"],["aam","application/x-authorware-map"],["aas","application/x-authorware-seg"],["abc","text/vnd.abc"],["abw","application/x-abiword"],["ac","application/pkix-attr-cert"],["acc","application/vnd.americandynamics.acc"],["ace","application/x-ace-compressed"],["acgi","text/html"],["acu","application/vnd.acucobol"],["acx","application/internet-property-stream"],["adp","audio/adpcm"],["aep","application/vnd.audiograph"],["afl","video/animaflex"],["afp","application/vnd.ibm.modcap"],["ahead","application/vnd.ahead.space"],["ai","application/postscript"],["aif",["audio/aiff","audio/x-aiff"]],["aifc",["audio/aiff","audio/x-aiff"]],["aiff",["audio/aiff","audio/x-aiff"]],["aim","application/x-aim"],["aip","text/x-audiosoft-intra"],["air","application/vnd.adobe.air-application-installer-package+zip"],["ait","application/vnd.dvb.ait"],["ami","application/vnd.amiga.ami"],["ani","application/x-navi-animation"],["aos","application/x-nokia-9000-communicator-add-on-software"],["apk","application/vnd.android.package-archive"],["application","application/x-ms-application"],["apr","application/vnd.lotus-approach"],["aps","application/mime"],["arc","application/octet-stream"],["arj",["application/arj","application/octet-stream"]],["art","image/x-jg"],["asf","video/x-ms-asf"],["asm","text/x-asm"],["aso","application/vnd.accpac.simply.aso"],["asp","text/asp"],["asr","video/x-ms-asf"],["asx",["video/x-ms-asf","application/x-mplayer2","video/x-ms-asf-plugin"]],["atc","application/vnd.acucorp"],["atomcat","application/atomcat+xml"],["atomsvc","application/atomsvc+xml"],["atx","application/vnd.antix.game-component"],["au",["audio/basic","audio/x-au"]],["avi",["video/avi","video/msvideo","application/x-troff-msvideo","video/x-msvideo"]],["avs","video/avs-video"],["aw","application/applixware"],["axs","application/olescript"],["azf","application/vnd.airzip.filesecure.azf"],["azs","application/vnd.airzip.filesecure.azs"],["azw","application/vnd.amazon.ebook"],["bas","text/plain"],["bcpio","application/x-bcpio"],["bdf","application/x-font-bdf"],["bdm","application/vnd.syncml.dm+wbxml"],["bed","application/vnd.realvnc.bed"],["bh2","application/vnd.fujitsu.oasysprs"],["bin",["application/octet-stream","application/mac-binary","application/macbinary","application/x-macbinary","application/x-binary"]],["bm","image/bmp"],["bmi","application/vnd.bmi"],["bmp",["image/bmp","image/x-windows-bmp"]],["boo","application/book"],["book","application/book"],["box","application/vnd.previewsystems.box"],["boz","application/x-bzip2"],["bsh","application/x-bsh"],["btif","image/prs.btif"],["bz","application/x-bzip"],["bz2","application/x-bzip2"],["c",["text/plain","text/x-c"]],["c++","text/plain"],["c11amc","application/vnd.cluetrust.cartomobile-config"],["c11amz","application/vnd.cluetrust.cartomobile-config-pkg"],["c4g","application/vnd.clonk.c4group"],["cab","application/vnd.ms-cab-compressed"],["car","application/vnd.curl.car"],["cat",["application/vnd.ms-pkiseccat","application/vnd.ms-pki.seccat"]],["cc",["text/plain","text/x-c"]],["ccad","application/clariscad"],["cco","application/x-cocoa"],["ccxml","application/ccxml+xml,"],["cdbcmsg","application/vnd.contact.cmsg"],["cdf",["application/cdf","application/x-cdf","application/x-netcdf"]],["cdkey","application/vnd.mediastation.cdkey"],["cdmia","application/cdmi-capability"],["cdmic","application/cdmi-container"],["cdmid","application/cdmi-domain"],["cdmio","application/cdmi-object"],["cdmiq","application/cdmi-queue"],["cdx","chemical/x-cdx"],["cdxml","application/vnd.chemdraw+xml"],["cdy","application/vnd.cinderella"],["cer",["application/pkix-cert","application/x-x509-ca-cert"]],["cgm","image/cgm"],["cha","application/x-chat"],["chat","application/x-chat"],["chm","application/vnd.ms-htmlhelp"],["chrt","application/vnd.kde.kchart"],["cif","chemical/x-cif"],["cii","application/vnd.anser-web-certificate-issue-initiation"],["cil","application/vnd.ms-artgalry"],["cla","application/vnd.claymore"],["class",["application/octet-stream","application/java","application/java-byte-code","application/java-vm","application/x-java-class"]],["clkk","application/vnd.crick.clicker.keyboard"],["clkp","application/vnd.crick.clicker.palette"],["clkt","application/vnd.crick.clicker.template"],["clkw","application/vnd.crick.clicker.wordbank"],["clkx","application/vnd.crick.clicker"],["clp","application/x-msclip"],["cmc","application/vnd.cosmocaller"],["cmdf","chemical/x-cmdf"],["cml","chemical/x-cml"],["cmp","application/vnd.yellowriver-custom-menu"],["cmx","image/x-cmx"],["cod",["image/cis-cod","application/vnd.rim.cod"]],["com",["application/octet-stream","text/plain"]],["conf","text/plain"],["cpio","application/x-cpio"],["cpp","text/x-c"],["cpt",["application/mac-compactpro","application/x-compactpro","application/x-cpt"]],["crd","application/x-mscardfile"],["crl",["application/pkix-crl","application/pkcs-crl"]],["crt",["application/pkix-cert","application/x-x509-user-cert","application/x-x509-ca-cert"]],["cryptonote","application/vnd.rig.cryptonote"],["csh",["text/x-script.csh","application/x-csh"]],["csml","chemical/x-csml"],["csp","application/vnd.commonspace"],["css",["text/css","application/x-pointplus"]],["csv","text/csv"],["cu","application/cu-seeme"],["curl","text/vnd.curl"],["cww","application/prs.cww"],["cxx","text/plain"],["dae","model/vnd.collada+xml"],["daf","application/vnd.mobius.daf"],["davmount","application/davmount+xml"],["dcr","application/x-director"],["dcurl","text/vnd.curl.dcurl"],["dd2","application/vnd.oma.dd2+xml"],["ddd","application/vnd.fujixerox.ddd"],["deb","application/x-debian-package"],["deepv","application/x-deepv"],["def","text/plain"],["der","application/x-x509-ca-cert"],["dfac","application/vnd.dreamfactory"],["dif","video/x-dv"],["dir","application/x-director"],["dis","application/vnd.mobius.dis"],["djvu","image/vnd.djvu"],["dl",["video/dl","video/x-dl"]],["dll","application/x-msdownload"],["dms","application/octet-stream"],["dna","application/vnd.dna"],["doc","application/msword"],["docm","application/vnd.ms-word.document.macroenabled.12"],["docx","application/vnd.openxmlformats-officedocument.wordprocessingml.document"],["dot","application/msword"],["dotm","application/vnd.ms-word.template.macroenabled.12"],["dotx","application/vnd.openxmlformats-officedocument.wordprocessingml.template"],["dp",["application/commonground","application/vnd.osgi.dp"]],["dpg","application/vnd.dpgraph"],["dra","audio/vnd.dra"],["drw","application/drafting"],["dsc","text/prs.lines.tag"],["dssc","application/dssc+der"],["dtb","application/x-dtbook+xml"],["dtd","application/xml-dtd"],["dts","audio/vnd.dts"],["dtshd","audio/vnd.dts.hd"],["dump","application/octet-stream"],["dv","video/x-dv"],["dvi","application/x-dvi"],["dwf",["model/vnd.dwf","drawing/x-dwf"]],["dwg",["application/acad","image/vnd.dwg","image/x-dwg"]],["dxf",["application/dxf","image/vnd.dwg","image/vnd.dxf","image/x-dwg"]],["dxp","application/vnd.spotfire.dxp"],["dxr","application/x-director"],["ecelp4800","audio/vnd.nuera.ecelp4800"],["ecelp7470","audio/vnd.nuera.ecelp7470"],["ecelp9600","audio/vnd.nuera.ecelp9600"],["edm","application/vnd.novadigm.edm"],["edx","application/vnd.novadigm.edx"],["efif","application/vnd.picsel"],["ei6","application/vnd.pg.osasli"],["el","text/x-script.elisp"],["elc",["application/x-elc","application/x-bytecode.elisp"]],["eml","message/rfc822"],["emma","application/emma+xml"],["env","application/x-envoy"],["eol","audio/vnd.digital-winds"],["eot","application/vnd.ms-fontobject"],["eps","application/postscript"],["epub","application/epub+zip"],["es",["application/ecmascript","application/x-esrehber"]],["es3","application/vnd.eszigno3+xml"],["esf","application/vnd.epson.esf"],["etx","text/x-setext"],["evy",["application/envoy","application/x-envoy"]],["exe",["application/octet-stream","application/x-msdownload"]],["exi","application/exi"],["ext","application/vnd.novadigm.ext"],["ez2","application/vnd.ezpix-album"],["ez3","application/vnd.ezpix-package"],["f",["text/plain","text/x-fortran"]],["f4v","video/x-f4v"],["f77","text/x-fortran"],["f90",["text/plain","text/x-fortran"]],["fbs","image/vnd.fastbidsheet"],["fcs","application/vnd.isac.fcs"],["fdf","application/vnd.fdf"],["fe_launch","application/vnd.denovo.fcselayout-link"],["fg5","application/vnd.fujitsu.oasysgp"],["fh","image/x-freehand"],["fif",["application/fractals","image/fif"]],["fig","application/x-xfig"],["fli",["video/fli","video/x-fli"]],["flo",["image/florian","application/vnd.micrografx.flo"]],["flr","x-world/x-vrml"],["flv","video/x-flv"],["flw","application/vnd.kde.kivio"],["flx","text/vnd.fmi.flexstor"],["fly","text/vnd.fly"],["fm","application/vnd.framemaker"],["fmf","video/x-atomic3d-feature"],["fnc","application/vnd.frogans.fnc"],["for",["text/plain","text/x-fortran"]],["fpx",["image/vnd.fpx","image/vnd.net-fpx"]],["frl","application/freeloader"],["fsc","application/vnd.fsc.weblaunch"],["fst","image/vnd.fst"],["ftc","application/vnd.fluxtime.clip"],["fti","application/vnd.anser-web-funds-transfer-initiation"],["funk","audio/make"],["fvt","video/vnd.fvt"],["fxp","application/vnd.adobe.fxp"],["fzs","application/vnd.fuzzysheet"],["g","text/plain"],["g2w","application/vnd.geoplan"],["g3","image/g3fax"],["g3w","application/vnd.geospace"],["gac","application/vnd.groove-account"],["gdl","model/vnd.gdl"],["geo","application/vnd.dynageo"],["geojson","application/geo+json"],["gex","application/vnd.geometry-explorer"],["ggb","application/vnd.geogebra.file"],["ggt","application/vnd.geogebra.tool"],["ghf","application/vnd.groove-help"],["gif","image/gif"],["gim","application/vnd.groove-identity-message"],["gl",["video/gl","video/x-gl"]],["gmx","application/vnd.gmx"],["gnumeric","application/x-gnumeric"],["gph","application/vnd.flographit"],["gqf","application/vnd.grafeq"],["gram","application/srgs"],["grv","application/vnd.groove-injector"],["grxml","application/srgs+xml"],["gsd","audio/x-gsm"],["gsf","application/x-font-ghostscript"],["gsm","audio/x-gsm"],["gsp","application/x-gsp"],["gss","application/x-gss"],["gtar","application/x-gtar"],["gtm","application/vnd.groove-tool-message"],["gtw","model/vnd.gtw"],["gv","text/vnd.graphviz"],["gxt","application/vnd.geonext"],["gz",["application/x-gzip","application/x-compressed"]],["gzip",["multipart/x-gzip","application/x-gzip"]],["h",["text/plain","text/x-h"]],["h261","video/h261"],["h263","video/h263"],["h264","video/h264"],["hal","application/vnd.hal+xml"],["hbci","application/vnd.hbci"],["hdf","application/x-hdf"],["help","application/x-helpfile"],["hgl","application/vnd.hp-hpgl"],["hh",["text/plain","text/x-h"]],["hlb","text/x-script"],["hlp",["application/winhlp","application/hlp","application/x-helpfile","application/x-winhelp"]],["hpg","application/vnd.hp-hpgl"],["hpgl","application/vnd.hp-hpgl"],["hpid","application/vnd.hp-hpid"],["hps","application/vnd.hp-hps"],["hqx",["application/mac-binhex40","application/binhex","application/binhex4","application/mac-binhex","application/x-binhex40","application/x-mac-binhex40"]],["hta","application/hta"],["htc","text/x-component"],["htke","application/vnd.kenameaapp"],["htm","text/html"],["html","text/html"],["htmls","text/html"],["htt","text/webviewhtml"],["htx","text/html"],["hvd","application/vnd.yamaha.hv-dic"],["hvp","application/vnd.yamaha.hv-voice"],["hvs","application/vnd.yamaha.hv-script"],["i2g","application/vnd.intergeo"],["icc","application/vnd.iccprofile"],["ice","x-conference/x-cooltalk"],["ico","image/x-icon"],["ics","text/calendar"],["idc","text/plain"],["ief","image/ief"],["iefs","image/ief"],["ifm","application/vnd.shana.informed.formdata"],["iges",["application/iges","model/iges"]],["igl","application/vnd.igloader"],["igm","application/vnd.insors.igm"],["igs",["application/iges","model/iges"]],["igx","application/vnd.micrografx.igx"],["iif","application/vnd.shana.informed.interchange"],["iii","application/x-iphone"],["ima","application/x-ima"],["imap","application/x-httpd-imap"],["imp","application/vnd.accpac.simply.imp"],["ims","application/vnd.ms-ims"],["inf","application/inf"],["ins",["application/x-internet-signup","application/x-internett-signup"]],["ip","application/x-ip2"],["ipfix","application/ipfix"],["ipk","application/vnd.shana.informed.package"],["irm","application/vnd.ibm.rights-management"],["irp","application/vnd.irepository.package+xml"],["isp","application/x-internet-signup"],["isu","video/x-isvideo"],["it","audio/it"],["itp","application/vnd.shana.informed.formtemplate"],["iv","application/x-inventor"],["ivp","application/vnd.immervision-ivp"],["ivr","i-world/i-vrml"],["ivu","application/vnd.immervision-ivu"],["ivy","application/x-livescreen"],["jad","text/vnd.sun.j2me.app-descriptor"],["jam",["application/vnd.jam","audio/x-jam"]],["jar","application/java-archive"],["jav",["text/plain","text/x-java-source"]],["java",["text/plain","text/x-java-source,java","text/x-java-source"]],["jcm","application/x-java-commerce"],["jfif",["image/pipeg","image/jpeg","image/pjpeg"]],["jfif-tbnl","image/jpeg"],["jisp","application/vnd.jisp"],["jlt","application/vnd.hp-jlyt"],["jnlp","application/x-java-jnlp-file"],["joda","application/vnd.joost.joda-archive"],["jpe",["image/jpeg","image/pjpeg"]],["jpeg",["image/jpeg","image/pjpeg"]],["jpg",["image/jpeg","image/pjpeg"]],["jpgv","video/jpeg"],["jpm","video/jpm"],["jps","image/x-jps"],["js",["application/javascript","application/ecmascript","text/javascript","text/ecmascript","application/x-javascript"]],["json","application/json"],["jut","image/jutvision"],["kar",["audio/midi","music/x-karaoke"]],["karbon","application/vnd.kde.karbon"],["kfo","application/vnd.kde.kformula"],["kia","application/vnd.kidspiration"],["kml","application/vnd.google-earth.kml+xml"],["kmz","application/vnd.google-earth.kmz"],["kne","application/vnd.kinar"],["kon","application/vnd.kde.kontour"],["kpr","application/vnd.kde.kpresenter"],["ksh",["application/x-ksh","text/x-script.ksh"]],["ksp","application/vnd.kde.kspread"],["ktx","image/ktx"],["ktz","application/vnd.kahootz"],["kwd","application/vnd.kde.kword"],["la",["audio/nspaudio","audio/x-nspaudio"]],["lam","audio/x-liveaudio"],["lasxml","application/vnd.las.las+xml"],["latex","application/x-latex"],["lbd","application/vnd.llamagraphics.life-balance.desktop"],["lbe","application/vnd.llamagraphics.life-balance.exchange+xml"],["les","application/vnd.hhe.lesson-player"],["lha",["application/octet-stream","application/lha","application/x-lha"]],["lhx","application/octet-stream"],["link66","application/vnd.route66.link66+xml"],["list","text/plain"],["lma",["audio/nspaudio","audio/x-nspaudio"]],["log","text/plain"],["lrm","application/vnd.ms-lrm"],["lsf","video/x-la-asf"],["lsp",["application/x-lisp","text/x-script.lisp"]],["lst","text/plain"],["lsx",["video/x-la-asf","text/x-la-asf"]],["ltf","application/vnd.frogans.ltf"],["ltx","application/x-latex"],["lvp","audio/vnd.lucent.voice"],["lwp","application/vnd.lotus-wordpro"],["lzh",["application/octet-stream","application/x-lzh"]],["lzx",["application/lzx","application/octet-stream","application/x-lzx"]],["m",["text/plain","text/x-m"]],["m13","application/x-msmediaview"],["m14","application/x-msmediaview"],["m1v","video/mpeg"],["m21","application/mp21"],["m2a","audio/mpeg"],["m2v","video/mpeg"],["m3u",["audio/x-mpegurl","audio/x-mpequrl"]],["m3u8","application/vnd.apple.mpegurl"],["m4v","video/x-m4v"],["ma","application/mathematica"],["mads","application/mads+xml"],["mag","application/vnd.ecowin.chart"],["man","application/x-troff-man"],["map","application/x-navimap"],["mar","text/plain"],["mathml","application/mathml+xml"],["mbd","application/mbedlet"],["mbk","application/vnd.mobius.mbk"],["mbox","application/mbox"],["mc$","application/x-magic-cap-package-1.0"],["mc1","application/vnd.medcalcdata"],["mcd",["application/mcad","application/vnd.mcd","application/x-mathcad"]],["mcf",["image/vasa","text/mcf"]],["mcp","application/netmc"],["mcurl","text/vnd.curl.mcurl"],["mdb","application/x-msaccess"],["mdi","image/vnd.ms-modi"],["me","application/x-troff-me"],["meta4","application/metalink4+xml"],["mets","application/mets+xml"],["mfm","application/vnd.mfmp"],["mgp","application/vnd.osgeo.mapguide.package"],["mgz","application/vnd.proteus.magazine"],["mht","message/rfc822"],["mhtml","message/rfc822"],["mid",["audio/mid","audio/midi","music/crescendo","x-music/x-midi","audio/x-midi","application/x-midi","audio/x-mid"]],["midi",["audio/midi","music/crescendo","x-music/x-midi","audio/x-midi","application/x-midi","audio/x-mid"]],["mif",["application/vnd.mif","application/x-mif","application/x-frame"]],["mime",["message/rfc822","www/mime"]],["mj2","video/mj2"],["mjf","audio/x-vnd.audioexplosion.mjuicemediafile"],["mjpg","video/x-motion-jpeg"],["mlp","application/vnd.dolby.mlp"],["mm",["application/base64","application/x-meme"]],["mmd","application/vnd.chipnuts.karaoke-mmd"],["mme","application/base64"],["mmf","application/vnd.smaf"],["mmr","image/vnd.fujixerox.edmics-mmr"],["mny","application/x-msmoney"],["mod",["audio/mod","audio/x-mod"]],["mods","application/mods+xml"],["moov","video/quicktime"],["mov","video/quicktime"],["movie","video/x-sgi-movie"],["mp2",["video/mpeg","audio/mpeg","video/x-mpeg","audio/x-mpeg","video/x-mpeq2a"]],["mp3",["audio/mpeg","audio/mpeg3","video/mpeg","audio/x-mpeg-3","video/x-mpeg"]],["mp4",["video/mp4","application/mp4"]],["mp4a","audio/mp4"],["mpa",["video/mpeg","audio/mpeg"]],["mpc",["application/vnd.mophun.certificate","application/x-project"]],["mpe","video/mpeg"],["mpeg","video/mpeg"],["mpg",["video/mpeg","audio/mpeg"]],["mpga","audio/mpeg"],["mpkg","application/vnd.apple.installer+xml"],["mpm","application/vnd.blueice.multipass"],["mpn","application/vnd.mophun.application"],["mpp","application/vnd.ms-project"],["mpt","application/x-project"],["mpv","application/x-project"],["mpv2","video/mpeg"],["mpx","application/x-project"],["mpy","application/vnd.ibm.minipay"],["mqy","application/vnd.mobius.mqy"],["mrc","application/marc"],["mrcx","application/marcxml+xml"],["ms","application/x-troff-ms"],["mscml","application/mediaservercontrol+xml"],["mseq","application/vnd.mseq"],["msf","application/vnd.epson.msf"],["msg","application/vnd.ms-outlook"],["msh","model/mesh"],["msl","application/vnd.mobius.msl"],["msty","application/vnd.muvee.style"],["mts","model/vnd.mts"],["mus","application/vnd.musician"],["musicxml","application/vnd.recordare.musicxml+xml"],["mv","video/x-sgi-movie"],["mvb","application/x-msmediaview"],["mwf","application/vnd.mfer"],["mxf","application/mxf"],["mxl","application/vnd.recordare.musicxml"],["mxml","application/xv+xml"],["mxs","application/vnd.triscape.mxs"],["mxu","video/vnd.mpegurl"],["my","audio/make"],["mzz","application/x-vnd.audioexplosion.mzz"],["n-gage","application/vnd.nokia.n-gage.symbian.install"],["n3","text/n3"],["nap","image/naplps"],["naplps","image/naplps"],["nbp","application/vnd.wolfram.player"],["nc","application/x-netcdf"],["ncm","application/vnd.nokia.configuration-message"],["ncx","application/x-dtbncx+xml"],["ngdat","application/vnd.nokia.n-gage.data"],["nif","image/x-niff"],["niff","image/x-niff"],["nix","application/x-mix-transfer"],["nlu","application/vnd.neurolanguage.nlu"],["nml","application/vnd.enliven"],["nnd","application/vnd.noblenet-directory"],["nns","application/vnd.noblenet-sealer"],["nnw","application/vnd.noblenet-web"],["npx","image/vnd.net-fpx"],["nsc","application/x-conference"],["nsf","application/vnd.lotus-notes"],["nvd","application/x-navidoc"],["nws","message/rfc822"],["o","application/octet-stream"],["oa2","application/vnd.fujitsu.oasys2"],["oa3","application/vnd.fujitsu.oasys3"],["oas","application/vnd.fujitsu.oasys"],["obd","application/x-msbinder"],["oda","application/oda"],["odb","application/vnd.oasis.opendocument.database"],["odc","application/vnd.oasis.opendocument.chart"],["odf","application/vnd.oasis.opendocument.formula"],["odft","application/vnd.oasis.opendocument.formula-template"],["odg","application/vnd.oasis.opendocument.graphics"],["odi","application/vnd.oasis.opendocument.image"],["odm","application/vnd.oasis.opendocument.text-master"],["odp","application/vnd.oasis.opendocument.presentation"],["ods","application/vnd.oasis.opendocument.spreadsheet"],["odt","application/vnd.oasis.opendocument.text"],["oga","audio/ogg"],["ogv","video/ogg"],["ogx","application/ogg"],["omc","application/x-omc"],["omcd","application/x-omcdatamaker"],["omcr","application/x-omcregerator"],["onetoc","application/onenote"],["opf","application/oebps-package+xml"],["org","application/vnd.lotus-organizer"],["osf","application/vnd.yamaha.openscoreformat"],["osfpvg","application/vnd.yamaha.openscoreformat.osfpvg+xml"],["otc","application/vnd.oasis.opendocument.chart-template"],["otf","application/x-font-otf"],["otg","application/vnd.oasis.opendocument.graphics-template"],["oth","application/vnd.oasis.opendocument.text-web"],["oti","application/vnd.oasis.opendocument.image-template"],["otp","application/vnd.oasis.opendocument.presentation-template"],["ots","application/vnd.oasis.opendocument.spreadsheet-template"],["ott","application/vnd.oasis.opendocument.text-template"],["oxt","application/vnd.openofficeorg.extension"],["p","text/x-pascal"],["p10",["application/pkcs10","application/x-pkcs10"]],["p12",["application/pkcs-12","application/x-pkcs12"]],["p7a","application/x-pkcs7-signature"],["p7b","application/x-pkcs7-certificates"],["p7c",["application/pkcs7-mime","application/x-pkcs7-mime"]],["p7m",["application/pkcs7-mime","application/x-pkcs7-mime"]],["p7r","application/x-pkcs7-certreqresp"],["p7s",["application/pkcs7-signature","application/x-pkcs7-signature"]],["p8","application/pkcs8"],["par","text/plain-bas"],["part","application/pro_eng"],["pas","text/pascal"],["paw","application/vnd.pawaafile"],["pbd","application/vnd.powerbuilder6"],["pbm","image/x-portable-bitmap"],["pcf","application/x-font-pcf"],["pcl",["application/vnd.hp-pcl","application/x-pcl"]],["pclxl","application/vnd.hp-pclxl"],["pct","image/x-pict"],["pcurl","application/vnd.curl.pcurl"],["pcx","image/x-pcx"],["pdb",["application/vnd.palm","chemical/x-pdb"]],["pdf","application/pdf"],["pfa","application/x-font-type1"],["pfr","application/font-tdpfr"],["pfunk",["audio/make","audio/make.my.funk"]],["pfx","application/x-pkcs12"],["pgm",["image/x-portable-graymap","image/x-portable-greymap"]],["pgn","application/x-chess-pgn"],["pgp","application/pgp-signature"],["pic",["image/pict","image/x-pict"]],["pict","image/pict"],["pkg","application/x-newton-compatible-pkg"],["pki","application/pkixcmp"],["pkipath","application/pkix-pkipath"],["pko",["application/ynd.ms-pkipko","application/vnd.ms-pki.pko"]],["pl",["text/plain","text/x-script.perl"]],["plb","application/vnd.3gpp.pic-bw-large"],["plc","application/vnd.mobius.plc"],["plf","application/vnd.pocketlearn"],["pls","application/pls+xml"],["plx","application/x-pixclscript"],["pm",["text/x-script.perl-module","image/x-xpixmap"]],["pm4","application/x-pagemaker"],["pm5","application/x-pagemaker"],["pma","application/x-perfmon"],["pmc","application/x-perfmon"],["pml",["application/vnd.ctc-posml","application/x-perfmon"]],["pmr","application/x-perfmon"],["pmw","application/x-perfmon"],["png","image/png"],["pnm",["application/x-portable-anymap","image/x-portable-anymap"]],["portpkg","application/vnd.macports.portpkg"],["pot",["application/vnd.ms-powerpoint","application/mspowerpoint"]],["potm","application/vnd.ms-powerpoint.template.macroenabled.12"],["potx","application/vnd.openxmlformats-officedocument.presentationml.template"],["pov","model/x-pov"],["ppa","application/vnd.ms-powerpoint"],["ppam","application/vnd.ms-powerpoint.addin.macroenabled.12"],["ppd","application/vnd.cups-ppd"],["ppm","image/x-portable-pixmap"],["pps",["application/vnd.ms-powerpoint","application/mspowerpoint"]],["ppsm","application/vnd.ms-powerpoint.slideshow.macroenabled.12"],["ppsx","application/vnd.openxmlformats-officedocument.presentationml.slideshow"],["ppt",["application/vnd.ms-powerpoint","application/mspowerpoint","application/powerpoint","application/x-mspowerpoint"]],["pptm","application/vnd.ms-powerpoint.presentation.macroenabled.12"],["pptx","application/vnd.openxmlformats-officedocument.presentationml.presentation"],["ppz","application/mspowerpoint"],["prc","application/x-mobipocket-ebook"],["pre",["application/vnd.lotus-freelance","application/x-freelance"]],["prf","application/pics-rules"],["prt","application/pro_eng"],["ps","application/postscript"],["psb","application/vnd.3gpp.pic-bw-small"],["psd",["application/octet-stream","image/vnd.adobe.photoshop"]],["psf","application/x-font-linux-psf"],["pskcxml","application/pskc+xml"],["ptid","application/vnd.pvi.ptid1"],["pub","application/x-mspublisher"],["pvb","application/vnd.3gpp.pic-bw-var"],["pvu","paleovu/x-pv"],["pwn","application/vnd.3m.post-it-notes"],["pwz","application/vnd.ms-powerpoint"],["py","text/x-script.phyton"],["pya","audio/vnd.ms-playready.media.pya"],["pyc","application/x-bytecode.python"],["pyv","video/vnd.ms-playready.media.pyv"],["qam","application/vnd.epson.quickanime"],["qbo","application/vnd.intu.qbo"],["qcp","audio/vnd.qcelp"],["qd3","x-world/x-3dmf"],["qd3d","x-world/x-3dmf"],["qfx","application/vnd.intu.qfx"],["qif","image/x-quicktime"],["qps","application/vnd.publishare-delta-tree"],["qt","video/quicktime"],["qtc","video/x-qtc"],["qti","image/x-quicktime"],["qtif","image/x-quicktime"],["qxd","application/vnd.quark.quarkxpress"],["ra",["audio/x-realaudio","audio/x-pn-realaudio","audio/x-pn-realaudio-plugin"]],["ram","audio/x-pn-realaudio"],["rar","application/x-rar-compressed"],["ras",["image/cmu-raster","application/x-cmu-raster","image/x-cmu-raster"]],["rast","image/cmu-raster"],["rcprofile","application/vnd.ipunplugged.rcprofile"],["rdf","application/rdf+xml"],["rdz","application/vnd.data-vision.rdz"],["rep","application/vnd.businessobjects"],["res","application/x-dtbresource+xml"],["rexx","text/x-script.rexx"],["rf","image/vnd.rn-realflash"],["rgb","image/x-rgb"],["rif","application/reginfo+xml"],["rip","audio/vnd.rip"],["rl","application/resource-lists+xml"],["rlc","image/vnd.fujixerox.edmics-rlc"],["rld","application/resource-lists-diff+xml"],["rm",["application/vnd.rn-realmedia","audio/x-pn-realaudio"]],["rmi","audio/mid"],["rmm","audio/x-pn-realaudio"],["rmp",["audio/x-pn-realaudio-plugin","audio/x-pn-realaudio"]],["rms","application/vnd.jcp.javame.midlet-rms"],["rnc","application/relax-ng-compact-syntax"],["rng",["application/ringing-tones","application/vnd.nokia.ringing-tone"]],["rnx","application/vnd.rn-realplayer"],["roff","application/x-troff"],["rp","image/vnd.rn-realpix"],["rp9","application/vnd.cloanto.rp9"],["rpm","audio/x-pn-realaudio-plugin"],["rpss","application/vnd.nokia.radio-presets"],["rpst","application/vnd.nokia.radio-preset"],["rq","application/sparql-query"],["rs","application/rls-services+xml"],["rsd","application/rsd+xml"],["rt",["text/richtext","text/vnd.rn-realtext"]],["rtf",["application/rtf","text/richtext","application/x-rtf"]],["rtx",["text/richtext","application/rtf"]],["rv","video/vnd.rn-realvideo"],["s","text/x-asm"],["s3m","audio/s3m"],["saf","application/vnd.yamaha.smaf-audio"],["saveme","application/octet-stream"],["sbk","application/x-tbook"],["sbml","application/sbml+xml"],["sc","application/vnd.ibm.secure-container"],["scd","application/x-msschedule"],["scm",["application/vnd.lotus-screencam","video/x-scm","text/x-script.guile","application/x-lotusscreencam","text/x-script.scheme"]],["scq","application/scvp-cv-request"],["scs","application/scvp-cv-response"],["sct","text/scriptlet"],["scurl","text/vnd.curl.scurl"],["sda","application/vnd.stardivision.draw"],["sdc","application/vnd.stardivision.calc"],["sdd","application/vnd.stardivision.impress"],["sdkm","application/vnd.solent.sdkm+xml"],["sdml","text/plain"],["sdp",["application/sdp","application/x-sdp"]],["sdr","application/sounder"],["sdw","application/vnd.stardivision.writer"],["sea",["application/sea","application/x-sea"]],["see","application/vnd.seemail"],["seed","application/vnd.fdsn.seed"],["sema","application/vnd.sema"],["semd","application/vnd.semd"],["semf","application/vnd.semf"],["ser","application/java-serialized-object"],["set","application/set"],["setpay","application/set-payment-initiation"],["setreg","application/set-registration-initiation"],["sfd-hdstx","application/vnd.hydrostatix.sof-data"],["sfs","application/vnd.spotfire.sfs"],["sgl","application/vnd.stardivision.writer-global"],["sgm",["text/sgml","text/x-sgml"]],["sgml",["text/sgml","text/x-sgml"]],["sh",["application/x-shar","application/x-bsh","application/x-sh","text/x-script.sh"]],["shar",["application/x-bsh","application/x-shar"]],["shf","application/shf+xml"],["shtml",["text/html","text/x-server-parsed-html"]],["sid","audio/x-psid"],["sis","application/vnd.symbian.install"],["sit",["application/x-stuffit","application/x-sit"]],["sitx","application/x-stuffitx"],["skd","application/x-koan"],["skm","application/x-koan"],["skp",["application/vnd.koan","application/x-koan"]],["skt","application/x-koan"],["sl","application/x-seelogo"],["sldm","application/vnd.ms-powerpoint.slide.macroenabled.12"],["sldx","application/vnd.openxmlformats-officedocument.presentationml.slide"],["slt","application/vnd.epson.salt"],["sm","application/vnd.stepmania.stepchart"],["smf","application/vnd.stardivision.math"],["smi",["application/smil","application/smil+xml"]],["smil","application/smil"],["snd",["audio/basic","audio/x-adpcm"]],["snf","application/x-font-snf"],["sol","application/solids"],["spc",["text/x-speech","application/x-pkcs7-certificates"]],["spf","application/vnd.yamaha.smaf-phrase"],["spl",["application/futuresplash","application/x-futuresplash"]],["spot","text/vnd.in3d.spot"],["spp","application/scvp-vp-response"],["spq","application/scvp-vp-request"],["spr","application/x-sprite"],["sprite","application/x-sprite"],["src","application/x-wais-source"],["sru","application/sru+xml"],["srx","application/sparql-results+xml"],["sse","application/vnd.kodak-descriptor"],["ssf","application/vnd.epson.ssf"],["ssi","text/x-server-parsed-html"],["ssm","application/streamingmedia"],["ssml","application/ssml+xml"],["sst",["application/vnd.ms-pkicertstore","application/vnd.ms-pki.certstore"]],["st","application/vnd.sailingtracker.track"],["stc","application/vnd.sun.xml.calc.template"],["std","application/vnd.sun.xml.draw.template"],["step","application/step"],["stf","application/vnd.wt.stf"],["sti","application/vnd.sun.xml.impress.template"],["stk","application/hyperstudio"],["stl",["application/vnd.ms-pkistl","application/sla","application/vnd.ms-pki.stl","application/x-navistyle"]],["stm","text/html"],["stp","application/step"],["str","application/vnd.pg.format"],["stw","application/vnd.sun.xml.writer.template"],["sub","image/vnd.dvb.subtitle"],["sus","application/vnd.sus-calendar"],["sv4cpio","application/x-sv4cpio"],["sv4crc","application/x-sv4crc"],["svc","application/vnd.dvb.service"],["svd","application/vnd.svd"],["svf",["image/vnd.dwg","image/x-dwg"]],["svg","image/svg+xml"],["svr",["x-world/x-svr","application/x-world"]],["swf","application/x-shockwave-flash"],["swi","application/vnd.aristanetworks.swi"],["sxc","application/vnd.sun.xml.calc"],["sxd","application/vnd.sun.xml.draw"],["sxg","application/vnd.sun.xml.writer.global"],["sxi","application/vnd.sun.xml.impress"],["sxm","application/vnd.sun.xml.math"],["sxw","application/vnd.sun.xml.writer"],["t",["text/troff","application/x-troff"]],["talk","text/x-speech"],["tao","application/vnd.tao.intent-module-archive"],["tar","application/x-tar"],["tbk",["application/toolbook","application/x-tbook"]],["tcap","application/vnd.3gpp2.tcap"],["tcl",["text/x-script.tcl","application/x-tcl"]],["tcsh","text/x-script.tcsh"],["teacher","application/vnd.smart.teacher"],["tei","application/tei+xml"],["tex","application/x-tex"],["texi","application/x-texinfo"],["texinfo","application/x-texinfo"],["text",["application/plain","text/plain"]],["tfi","application/thraud+xml"],["tfm","application/x-tex-tfm"],["tgz",["application/gnutar","application/x-compressed"]],["thmx","application/vnd.ms-officetheme"],["tif",["image/tiff","image/x-tiff"]],["tiff",["image/tiff","image/x-tiff"]],["tmo","application/vnd.tmobile-livetv"],["torrent","application/x-bittorrent"],["tpl","application/vnd.groove-tool-template"],["tpt","application/vnd.trid.tpt"],["tr","application/x-troff"],["tra","application/vnd.trueapp"],["trm","application/x-msterminal"],["tsd","application/timestamped-data"],["tsi","audio/tsp-audio"],["tsp",["application/dsptype","audio/tsplayer"]],["tsv","text/tab-separated-values"],["ttf","application/x-font-ttf"],["ttl","text/turtle"],["turbot","image/florian"],["twd","application/vnd.simtech-mindmapper"],["txd","application/vnd.genomatix.tuxedo"],["txf","application/vnd.mobius.txf"],["txt","text/plain"],["ufd","application/vnd.ufdl"],["uil","text/x-uil"],["uls","text/iuls"],["umj","application/vnd.umajin"],["uni","text/uri-list"],["unis","text/uri-list"],["unityweb","application/vnd.unity"],["unv","application/i-deas"],["uoml","application/vnd.uoml+xml"],["uri","text/uri-list"],["uris","text/uri-list"],["ustar",["application/x-ustar","multipart/x-ustar"]],["utz","application/vnd.uiq.theme"],["uu",["application/octet-stream","text/x-uuencode"]],["uue","text/x-uuencode"],["uva","audio/vnd.dece.audio"],["uvh","video/vnd.dece.hd"],["uvi","image/vnd.dece.graphic"],["uvm","video/vnd.dece.mobile"],["uvp","video/vnd.dece.pd"],["uvs","video/vnd.dece.sd"],["uvu","video/vnd.uvvu.mp4"],["uvv","video/vnd.dece.video"],["vcd","application/x-cdlink"],["vcf","text/x-vcard"],["vcg","application/vnd.groove-vcard"],["vcs","text/x-vcalendar"],["vcx","application/vnd.vcx"],["vda","application/vda"],["vdo","video/vdo"],["vew","application/groupwise"],["vis","application/vnd.visionary"],["viv",["video/vivo","video/vnd.vivo"]],["vivo",["video/vivo","video/vnd.vivo"]],["vmd","application/vocaltec-media-desc"],["vmf","application/vocaltec-media-file"],["voc",["audio/voc","audio/x-voc"]],["vos","video/vosaic"],["vox","audio/voxware"],["vqe","audio/x-twinvq-plugin"],["vqf","audio/x-twinvq"],["vql","audio/x-twinvq-plugin"],["vrml",["model/vrml","x-world/x-vrml","application/x-vrml"]],["vrt","x-world/x-vrt"],["vsd",["application/vnd.visio","application/x-visio"]],["vsf","application/vnd.vsf"],["vst","application/x-visio"],["vsw","application/x-visio"],["vtu","model/vnd.vtu"],["vxml","application/voicexml+xml"],["w60","application/wordperfect6.0"],["w61","application/wordperfect6.1"],["w6w","application/msword"],["wad","application/x-doom"],["wav",["audio/wav","audio/x-wav"]],["wax","audio/x-ms-wax"],["wb1","application/x-qpro"],["wbmp","image/vnd.wap.wbmp"],["wbs","application/vnd.criticaltools.wbs+xml"],["wbxml","application/vnd.wap.wbxml"],["wcm","application/vnd.ms-works"],["wdb","application/vnd.ms-works"],["web","application/vnd.xara"],["weba","audio/webm"],["webm","video/webm"],["webp","image/webp"],["wg","application/vnd.pmi.widget"],["wgt","application/widget"],["wiz","application/msword"],["wk1","application/x-123"],["wks","application/vnd.ms-works"],["wm","video/x-ms-wm"],["wma","audio/x-ms-wma"],["wmd","application/x-ms-wmd"],["wmf",["windows/metafile","application/x-msmetafile"]],["wml","text/vnd.wap.wml"],["wmlc","application/vnd.wap.wmlc"],["wmls","text/vnd.wap.wmlscript"],["wmlsc","application/vnd.wap.wmlscriptc"],["wmv","video/x-ms-wmv"],["wmx","video/x-ms-wmx"],["wmz","application/x-ms-wmz"],["woff","application/x-font-woff"],["word","application/msword"],["wp","application/wordperfect"],["wp5",["application/wordperfect","application/wordperfect6.0"]],["wp6","application/wordperfect"],["wpd",["application/wordperfect","application/vnd.wordperfect","application/x-wpwin"]],["wpl","application/vnd.ms-wpl"],["wps","application/vnd.ms-works"],["wq1","application/x-lotus"],["wqd","application/vnd.wqd"],["wri",["application/mswrite","application/x-wri","application/x-mswrite"]],["wrl",["model/vrml","x-world/x-vrml","application/x-world"]],["wrz",["model/vrml","x-world/x-vrml"]],["wsc","text/scriplet"],["wsdl","application/wsdl+xml"],["wspolicy","application/wspolicy+xml"],["wsrc","application/x-wais-source"],["wtb","application/vnd.webturbo"],["wtk","application/x-wintalk"],["wvx","video/x-ms-wvx"],["x-png","image/png"],["x3d","application/vnd.hzn-3d-crossword"],["xaf","x-world/x-vrml"],["xap","application/x-silverlight-app"],["xar","application/vnd.xara"],["xbap","application/x-ms-xbap"],["xbd","application/vnd.fujixerox.docuworks.binder"],["xbm",["image/xbm","image/x-xbm","image/x-xbitmap"]],["xdf","application/xcap-diff+xml"],["xdm","application/vnd.syncml.dm+xml"],["xdp","application/vnd.adobe.xdp+xml"],["xdr","video/x-amt-demorun"],["xdssc","application/dssc+xml"],["xdw","application/vnd.fujixerox.docuworks"],["xenc","application/xenc+xml"],["xer","application/patch-ops-error+xml"],["xfdf","application/vnd.adobe.xfdf"],["xfdl","application/vnd.xfdl"],["xgz","xgl/drawing"],["xhtml","application/xhtml+xml"],["xif","image/vnd.xiff"],["xl","application/excel"],["xla",["application/vnd.ms-excel","application/excel","application/x-msexcel","application/x-excel"]],["xlam","application/vnd.ms-excel.addin.macroenabled.12"],["xlb",["application/excel","application/vnd.ms-excel","application/x-excel"]],["xlc",["application/vnd.ms-excel","application/excel","application/x-excel"]],["xld",["application/excel","application/x-excel"]],["xlk",["application/excel","application/x-excel"]],["xll",["application/excel","application/vnd.ms-excel","application/x-excel"]],["xlm",["application/vnd.ms-excel","application/excel","application/x-excel"]],["xls",["application/vnd.ms-excel","application/excel","application/x-msexcel","application/x-excel"]],["xlsb","application/vnd.ms-excel.sheet.binary.macroenabled.12"],["xlsm","application/vnd.ms-excel.sheet.macroenabled.12"],["xlsx","application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"],["xlt",["application/vnd.ms-excel","application/excel","application/x-excel"]],["xltm","application/vnd.ms-excel.template.macroenabled.12"],["xltx","application/vnd.openxmlformats-officedocument.spreadsheetml.template"],["xlv",["application/excel","application/x-excel"]],["xlw",["application/vnd.ms-excel","application/excel","application/x-msexcel","application/x-excel"]],["xm","audio/xm"],["xml",["application/xml","text/xml","application/atom+xml","application/rss+xml"]],["xmz","xgl/movie"],["xo","application/vnd.olpc-sugar"],["xof","x-world/x-vrml"],["xop","application/xop+xml"],["xpi","application/x-xpinstall"],["xpix","application/x-vnd.ls-xpix"],["xpm",["image/xpm","image/x-xpixmap"]],["xpr","application/vnd.is-xpr"],["xps","application/vnd.ms-xpsdocument"],["xpw","application/vnd.intercon.formnet"],["xslt","application/xslt+xml"],["xsm","application/vnd.syncml+xml"],["xspf","application/xspf+xml"],["xsr","video/x-amt-showrun"],["xul","application/vnd.mozilla.xul+xml"],["xwd",["image/x-xwd","image/x-xwindowdump"]],["xyz",["chemical/x-xyz","chemical/x-pdb"]],["yang","application/yang"],["yin","application/yin+xml"],["z",["application/x-compressed","application/x-compress"]],["zaz","application/vnd.zzazz.deck+xml"],["zip",["application/zip","multipart/x-zip","application/x-zip-compressed","application/x-compressed"]],["zir","application/vnd.zul"],["zmm","application/vnd.handheld-entertainment+xml"],["zoo","application/octet-stream"],["zsh","text/x-script.zsh"]]);e.exports={detectMimeType(e){if(!e)return s;let t=a.parse(e),i=(t.ext.substr(1)||t.name||"").split("?").shift().trim().toLowerCase(),n=s;return(o.has(i)&&(n=o.get(i)),Array.isArray(n))?n[0]:n},detectExtension(e){if(!e)return"bin";let t=(e||"").toLowerCase().trim().split("/"),i=t.shift().trim(),a=t.join("/").trim();if(n.has(i+"/"+a)){let e=n.get(i+"/"+a);return Array.isArray(e)?e[0]:e}return"text"===i?"txt":"bin"}}},50623:(e,t,i)=>{"use strict";i.r(t),i.d(t,{patchFetch:()=>b,routeModule:()=>x,serverHooks:()=>_,workAsyncStorage:()=>g,workUnitAsyncStorage:()=>v});var a={};i.r(a),i.d(a,{POST:()=>f});var s=i(96559),n=i(48088),o=i(37719),r=i(32190),p=i(75745),c=i(17063),l=i(49526),d=i(55511),m=i.n(d),h=i(80972);let u=l.createTransport({host:process.env.EMAIL_HOST,port:parseInt(process.env.EMAIL_PORT||"587"),secure:!1,auth:{user:process.env.EMAIL_USER,pass:process.env.EMAIL_PASS}});async function f(e){try{await (0,p.A)();let{email:t}=await e.json(),i=(0,h.y3)(e);if(!t||!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(t))return r.NextResponse.json({success:!1,error:(0,h.Q$)(i,"errors.invalid_request")},{status:400});let a=Math.floor(1e5+9e5*Math.random()).toString(),s=m().randomBytes(32).toString("hex"),n=new Date(Date.now()+6e5),o=await c.A.findOne({email:t.toLowerCase()});o?(o.emailVerificationToken=s,o.emailVerificationExpires=n):o=new c.A({email:t.toLowerCase(),name:t.split("@")[0],emailVerified:!1,emailVerificationToken:s,emailVerificationExpires:n}),await o.save();try{return await u.sendMail({to:t,from:process.env.EMAIL_FROM,subject:"AI Tools Directory - 登录验证码",html:`
          <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;">
            <div style="text-align: center; margin-bottom: 30px;">
              <h1 style="color: #3B82F6; margin: 0;">AI Tools Directory</h1>
            </div>
            
            <div style="background-color: #f8fafc; padding: 30px; border-radius: 8px; text-align: center;">
              <h2 style="color: #1f2937; margin-bottom: 20px;">您的登录验证码</h2>
              
              <div style="background-color: white; padding: 20px; border-radius: 6px; margin: 20px 0;">
                <span style="font-size: 32px; font-weight: bold; color: #3B82F6; letter-spacing: 8px;">
                  ${a}
                </span>
              </div>
              
              <p style="color: #6b7280; margin: 20px 0;">
                请在10分钟内输入此验证码完成登录
              </p>
              
              <p style="color: #ef4444; font-size: 14px; margin-top: 30px;">
                如果您没有请求此验证码，请忽略此邮件
              </p>
            </div>
            
            <div style="text-align: center; margin-top: 30px; color: #9ca3af; font-size: 12px;">
              <p>此邮件由 AI Tools Directory 自动发送，请勿回复</p>
            </div>
          </div>
        `,text:`您的 AI Tools Directory 登录验证码是：${a}。请在10分钟内使用此验证码完成登录。`}),o.emailVerificationToken=`${s}:${a}`,await o.save(),r.NextResponse.json({success:!0,message:(0,h.Q$)(i,"auth.code_sent"),token:s})}catch(e){return console.error("Email sending error:",e),r.NextResponse.json({success:!1,error:(0,h.Q$)(i,"auth.code_send_failed")},{status:500})}}catch(i){console.error("Send code error:",i);let t=(0,h.y3)(e);return r.NextResponse.json({success:!1,error:(0,h.Q$)(t,"errors.internal_error")},{status:500})}}let x=new s.AppRouteRouteModule({definition:{kind:n.RouteKind.APP_ROUTE,page:"/api/auth/send-code/route",pathname:"/api/auth/send-code",filename:"route",bundlePath:"app/api/auth/send-code/route"},resolvedPagePath:"/Users/<USER>/workspace/aitools/aitools-website/src/app/api/auth/send-code/route.ts",nextConfigOutput:"",userland:a}),{workAsyncStorage:g,workUnitAsyncStorage:v,serverHooks:_}=x;function b(){return(0,o.patchFetch)({workAsyncStorage:g,workUnitAsyncStorage:v})}},53752:(e,t,i)=>{"use strict";let a=i(79551);class s{constructor(e){this.options=e||{},this.cookies=[]}set(e,t){let i,s=a.parse(t||""),n=this.parse(e);return n.domain?(i=n.domain.replace(/^\./,""),(s.hostname.length<i.length||("."+s.hostname).substr(-i.length+1)!=="."+i)&&(n.domain=s.hostname)):n.domain=s.hostname,n.path||(n.path=this.getPath(s.pathname)),n.expires||(n.expires=new Date(Date.now()+1e3*(Number(this.options.sessionTimeout||1800)||1800))),this.add(n)}get(e){return this.list(e).map(e=>e.name+"="+e.value).join("; ")}list(e){let t,i,a=[];for(t=this.cookies.length-1;t>=0;t--){if(i=this.cookies[t],this.isExpired(i)){this.cookies.splice(t,t);continue}this.match(i,e)&&a.unshift(i)}return a}parse(e){let t={};return(e||"").toString().split(";").forEach(e=>{let i,a=e.split("="),s=a.shift().trim().toLowerCase(),n=a.join("=").trim();if(s)switch(s){case"expires":"Invalid Date"!==(n=new Date(n)).toString()&&(t.expires=n);break;case"path":t.path=n;break;case"domain":(i=n.toLowerCase()).length&&"."!==i.charAt(0)&&(i="."+i),t.domain=i;break;case"max-age":t.expires=new Date(Date.now()+1e3*(Number(n)||0));break;case"secure":t.secure=!0;break;case"httponly":t.httponly=!0;break;default:t.name||(t.name=s,t.value=n)}}),t}match(e,t){let i=a.parse(t||"");return(i.hostname===e.domain||"."===e.domain.charAt(0)&&("."+i.hostname).substr(-e.domain.length)===e.domain)&&this.getPath(i.pathname).substr(0,e.path.length)===e.path&&(!e.secure||"https:"===i.protocol)}add(e){let t,i;if(!e||!e.name)return!1;for(t=0,i=this.cookies.length;t<i;t++)if(this.compare(this.cookies[t],e)){if(this.isExpired(e))return this.cookies.splice(t,1),!1;return this.cookies[t]=e,!0}return this.isExpired(e)||this.cookies.push(e),!0}compare(e,t){return e.name===t.name&&e.path===t.path&&e.domain===t.domain&&e.secure===t.secure&&e.httponly==e.httponly}isExpired(e){return e.expires&&e.expires<new Date||!e.value}getPath(e){let t=(e||"/").split("/");return t.pop(),"/"!==(t=t.join("/").trim()).charAt(0)&&(t="/"+t),"/"!==t.substr(-1)&&(t+="/"),t}}e.exports=s},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},56037:e=>{"use strict";e.exports=require("mongoose")},58417:e=>{"use strict";e.exports=JSON.parse('{"126":{"host":"smtp.126.com","port":465,"secure":true},"163":{"host":"smtp.163.com","port":465,"secure":true},"1und1":{"host":"smtp.1und1.de","port":465,"secure":true,"authMethod":"LOGIN"},"Aliyun":{"domains":["aliyun.com"],"host":"smtp.aliyun.com","port":465,"secure":true},"AOL":{"domains":["aol.com"],"host":"smtp.aol.com","port":587},"Bluewin":{"host":"smtpauths.bluewin.ch","domains":["bluewin.ch"],"port":465},"DebugMail":{"host":"debugmail.io","port":25},"DynectEmail":{"aliases":["Dynect"],"host":"smtp.dynect.net","port":25},"Ethereal":{"aliases":["ethereal.email"],"host":"smtp.ethereal.email","port":587},"FastMail":{"domains":["fastmail.fm"],"host":"smtp.fastmail.com","port":465,"secure":true},"Forward Email":{"aliases":["FE","ForwardEmail"],"domains":["forwardemail.net"],"host":"smtp.forwardemail.net","port":465,"secure":true},"Feishu Mail":{"aliases":["Feishu","FeishuMail"],"domains":["www.feishu.cn"],"host":"smtp.feishu.cn","port":465,"secure":true},"GandiMail":{"aliases":["Gandi","Gandi Mail"],"host":"mail.gandi.net","port":587},"Gmail":{"aliases":["Google Mail"],"domains":["gmail.com","googlemail.com"],"host":"smtp.gmail.com","port":465,"secure":true},"Godaddy":{"host":"smtpout.secureserver.net","port":25},"GodaddyAsia":{"host":"smtp.asia.secureserver.net","port":25},"GodaddyEurope":{"host":"smtp.europe.secureserver.net","port":25},"hot.ee":{"host":"mail.hot.ee"},"Hotmail":{"aliases":["Outlook","Outlook.com","Hotmail.com"],"domains":["hotmail.com","outlook.com"],"host":"smtp-mail.outlook.com","port":587},"iCloud":{"aliases":["Me","Mac"],"domains":["me.com","mac.com"],"host":"smtp.mail.me.com","port":587},"Infomaniak":{"host":"mail.infomaniak.com","domains":["ik.me","ikmail.com","etik.com"],"port":587},"Loopia":{"host":"mailcluster.loopia.se","port":465},"mail.ee":{"host":"smtp.mail.ee"},"Mail.ru":{"host":"smtp.mail.ru","port":465,"secure":true},"Mailcatch.app":{"host":"sandbox-smtp.mailcatch.app","port":2525},"Maildev":{"port":1025,"ignoreTLS":true},"Mailgun":{"host":"smtp.mailgun.org","port":465,"secure":true},"Mailjet":{"host":"in.mailjet.com","port":587},"Mailosaur":{"host":"mailosaur.io","port":25},"Mailtrap":{"host":"live.smtp.mailtrap.io","port":587},"Mandrill":{"host":"smtp.mandrillapp.com","port":587},"Naver":{"host":"smtp.naver.com","port":587},"One":{"host":"send.one.com","port":465,"secure":true},"OpenMailBox":{"aliases":["OMB","openmailbox.org"],"host":"smtp.openmailbox.org","port":465,"secure":true},"Outlook365":{"host":"smtp.office365.com","port":587,"secure":false},"OhMySMTP":{"host":"smtp.ohmysmtp.com","port":587,"secure":false},"Postmark":{"aliases":["PostmarkApp"],"host":"smtp.postmarkapp.com","port":2525},"Proton":{"aliases":["ProtonMail","Proton.me","Protonmail.com","Protonmail.ch"],"domains":["proton.me","protonmail.com","pm.me","protonmail.ch"],"host":"smtp.protonmail.ch","port":587,"requireTLS":true},"qiye.aliyun":{"host":"smtp.mxhichina.com","port":"465","secure":true},"QQ":{"domains":["qq.com"],"host":"smtp.qq.com","port":465,"secure":true},"QQex":{"aliases":["QQ Enterprise"],"domains":["exmail.qq.com"],"host":"smtp.exmail.qq.com","port":465,"secure":true},"SendCloud":{"host":"smtp.sendcloud.net","port":2525},"SendGrid":{"host":"smtp.sendgrid.net","port":587},"SendinBlue":{"aliases":["Brevo"],"host":"smtp-relay.brevo.com","port":587},"SendPulse":{"host":"smtp-pulse.com","port":465,"secure":true},"SES":{"host":"email-smtp.us-east-1.amazonaws.com","port":465,"secure":true},"SES-US-EAST-1":{"host":"email-smtp.us-east-1.amazonaws.com","port":465,"secure":true},"SES-US-WEST-2":{"host":"email-smtp.us-west-2.amazonaws.com","port":465,"secure":true},"SES-EU-WEST-1":{"host":"email-smtp.eu-west-1.amazonaws.com","port":465,"secure":true},"SES-AP-SOUTH-1":{"host":"email-smtp.ap-south-1.amazonaws.com","port":465,"secure":true},"SES-AP-NORTHEAST-1":{"host":"email-smtp.ap-northeast-1.amazonaws.com","port":465,"secure":true},"SES-AP-NORTHEAST-2":{"host":"email-smtp.ap-northeast-2.amazonaws.com","port":465,"secure":true},"SES-AP-NORTHEAST-3":{"host":"email-smtp.ap-northeast-3.amazonaws.com","port":465,"secure":true},"SES-AP-SOUTHEAST-1":{"host":"email-smtp.ap-southeast-1.amazonaws.com","port":465,"secure":true},"SES-AP-SOUTHEAST-2":{"host":"email-smtp.ap-southeast-2.amazonaws.com","port":465,"secure":true},"Seznam":{"aliases":["Seznam Email"],"domains":["seznam.cz","email.cz","post.cz","spoluzaci.cz"],"host":"smtp.seznam.cz","port":465,"secure":true},"Sparkpost":{"aliases":["SparkPost","SparkPost Mail"],"domains":["sparkpost.com"],"host":"smtp.sparkpostmail.com","port":587,"secure":false},"Tipimail":{"host":"smtp.tipimail.com","port":587},"Yahoo":{"domains":["yahoo.com"],"host":"smtp.mail.yahoo.com","port":465,"secure":true},"Yandex":{"domains":["yandex.ru"],"host":"smtp.yandex.ru","port":465,"secure":true},"Zoho":{"host":"smtp.zoho.com","port":465,"secure":true,"authMethod":"LOGIN"}}')},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},63356:(e,t,i)=>{"use strict";let a=i(27910).Transform;function s(e){let t;"string"==typeof e&&(e=Buffer.from(e,"utf-8"));let i=[[9],[10],[13],[32,60],[62,126]],a="";for(let s=0,n=e.length;s<n;s++){if(function(e,t){for(let i=t.length-1;i>=0;i--)if(t[i].length&&(1===t[i].length&&e===t[i][0]||2===t[i].length&&e>=t[i][0]&&e<=t[i][1]))return!0;return!1}(t=e[s],i)&&(32!==t&&9!==t||s!==n-1&&10!==e[s+1]&&13!==e[s+1])){a+=String.fromCharCode(t);continue}a+="="+(t<16?"0":"")+t.toString(16).toUpperCase()}return a}function n(e,t){let i,a,s;if(e=(e||"").toString(),t=t||76,e.length<=t)return e;let n=0,o=e.length,r=Math.floor(t/3),p="";for(;n<o;){if(i=(s=e.substr(n,t)).match(/\r\n/)){p+=s=s.substr(0,i.index+i[0].length),n+=s.length;continue}if("\n"===s.substr(-1)){p+=s,n+=s.length;continue}if(i=s.substr(-r).match(/\n.*?$/)){p+=s=s.substr(0,s.length-(i[0].length-1)),n+=s.length;continue}if(s.length>t-r&&(i=s.substr(-r).match(/[ \t.,!?][^ \t.,!?]*$/)))s=s.substr(0,s.length-(i[0].length-1));else if(s.match(/[=][\da-f]{0,2}$/i))for((i=s.match(/[=][\da-f]{0,1}$/i))&&(s=s.substr(0,s.length-i[0].length));s.length>3&&s.length<o-n&&!s.match(/^(?:=[\da-f]{2}){1,4}$/i)&&(i=s.match(/[=][\da-f]{2}$/gi))&&!((a=parseInt(i[0].substr(1,2),16))<128)&&(s=s.substr(0,s.length-3),!(a>=192)););n+s.length<o&&"\n"!==s.substr(-1)?(s.length===t&&s.match(/[=][\da-f]{2}$/i)?s=s.substr(0,s.length-3):s.length===t&&(s=s.substr(0,s.length-1)),n+=s.length,s+="=\r\n"):n+=s.length,p+=s}return p}class o extends a{constructor(e){super(),this.options=e||{},!1!==this.options.lineLength&&(this.options.lineLength=this.options.lineLength||76),this._curLine="",this.inputBytes=0,this.outputBytes=0}_transform(e,t,i){let a;if("buffer"!==t&&(e=Buffer.from(e,t)),!e||!e.length)return i();this.inputBytes+=e.length,this.options.lineLength?(a=(a=n(a=this._curLine+s(e),this.options.lineLength)).replace(/(^|\n)([^\n]*)$/,(e,t,i)=>(this._curLine=i,t)))&&(this.outputBytes+=a.length,this.push(a)):(a=s(e),this.outputBytes+=a.length,this.push(a,"ascii")),i()}_flush(e){this._curLine&&(this.outputBytes+=this._curLine.length,this.push(this._curLine,"ascii")),e()}}e.exports={encode:s,wrap:n,Encoder:o}},72609:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var i in t)Object.defineProperty(e,i,{enumerable:!0,get:t[i]})}(t,{describeHasCheckingStringProperty:function(){return s},describeStringPropertyAccess:function(){return a},wellKnownProperties:function(){return n}});let i=/^[A-Za-z_$][A-Za-z0-9_$]*$/;function a(e,t){return i.test(t)?"`"+e+"."+t+"`":"`"+e+"["+JSON.stringify(t)+"]`"}function s(e,t){let i=JSON.stringify(t);return"`Reflect.has("+e+", "+i+")`, `"+i+" in "+e+"`, or similar"}let n=new Set(["hasOwnProperty","isPrototypeOf","propertyIsEnumerable","toString","valueOf","toLocaleString","then","catch","finally","status","displayName","toJSON","$$typeof","__esModule"])},74075:e=>{"use strict";e.exports=require("zlib")},74301:(e,t,i)=>{"use strict";let a=i(48178),s=i(18199),n=i(55511);function o(e,t,i){let a=new Set,s=new Set,n=new Map;(i||"").toLowerCase().split(":").forEach(e=>{s.add(e.trim())}),(t||"").toLowerCase().split(":").filter(e=>!s.has(e.trim())).forEach(e=>{a.add(e.trim())});for(let t=e.length-1;t>=0;t--){let i=e[t];a.has(i.key)&&!n.has(i.key)&&n.set(i.key,r(i.line))}let o=[],p=[];return a.forEach(e=>{n.has(e)&&(p.push(e),o.push(e+":"+n.get(e)))}),{headers:o.join("\r\n")+"\r\n",fieldNames:p.join(":")}}function r(e){return e.substr(e.indexOf(":")+1).replace(/\r?\n/g,"").replace(/\s+/g," ").trim()}e.exports=(e,t,i,p)=>{let c,l,d=o(e,(p=p||{}).headerFieldNames||"From:Sender:Reply-To:Subject:Date:Message-ID:To:Cc:MIME-Version:Content-Type:Content-Transfer-Encoding:Content-ID:Content-Description:Resent-Date:Resent-From:Resent-Sender:Resent-To:Resent-Cc:Resent-Message-ID:In-Reply-To:References:List-Id:List-Help:List-Unsubscribe:List-Subscribe:List-Post:List-Owner:List-Archive",p.skipFields),m=function(e,t,i,n,o){let r=["v=1","a=rsa-"+n,"c=relaxed/relaxed","d="+a.toASCII(e),"q=dns/txt","s="+t,"bh="+o,"h="+i].join("; ");return s.foldLines("DKIM-Signature: "+r,76)+";\r\n b="}(p.domainName,p.keySelector,d.fieldNames,t,i);d.headers+="dkim-signature:"+r(m),(c=n.createSign(("rsa-"+t).toUpperCase())).update(d.headers);try{l=c.sign(p.privateKey,"base64")}catch(e){return!1}return m+l.replace(/(^.{73}|.{75}(?!\r?\n|\r))/g,"$&\r\n ").trim()},e.exports.relaxedHeaders=o},74508:e=>{"use strict";class t{constructor(e){this.str=(e||"").toString(),this.operatorCurrent="",this.operatorExpecting="",this.node=null,this.escaped=!1,this.list=[],this.operators={'"':'"',"(":")","<":">",",":"",":":";",";":""}}tokenize(){let e=[];for(let e=0,t=this.str.length;e<t;e++){let i=this.str.charAt(e),a=e<t-1?this.str.charAt(e+1):null;this.checkChar(i,a)}return this.list.forEach(t=>{t.value=(t.value||"").toString().trim(),t.value&&e.push(t)}),e}checkChar(e,t){if(this.escaped);else if(e===this.operatorExpecting){this.node={type:"operator",value:e},t&&![" ","	","\r","\n",",",";"].includes(t)&&(this.node.noBreak=!0),this.list.push(this.node),this.node=null,this.operatorExpecting="",this.escaped=!1;return}else if(!this.operatorExpecting&&e in this.operators){this.node={type:"operator",value:e},this.list.push(this.node),this.node=null,this.operatorExpecting=this.operators[e],this.escaped=!1;return}else if(['"',"'"].includes(this.operatorExpecting)&&"\\"===e){this.escaped=!0;return}this.node||(this.node={type:"text",value:""},this.list.push(this.node)),"\n"===e&&(e=" "),(e.charCodeAt(0)>=33||[" ","	"].includes(e))&&(this.node.value+=e),this.escaped=!1}}e.exports=function e(i,a){a=a||{};let s=new t(i).tokenize(),n=[],o=[],r=[];if(s.forEach(e=>{"operator"===e.type&&(","===e.value||";"===e.value)?(o.length&&n.push(o),o=[]):o.push(e)}),o.length&&n.push(o),n.forEach(t=>{(t=function(t){let i,a,s,n=!1,o="text",r=[],p={address:[],comment:[],group:[],text:[]};for(a=0,s=t.length;a<s;a++){let e=t[a],i=a?t[a-1]:null;if("operator"===e.type)switch(e.value){case"<":o="address";break;case"(":o="comment";break;case":":o="group",n=!0;break;default:o="text"}else e.value&&("address"===o&&(e.value=e.value.replace(/^[^<]*<\s*/,"")),i&&i.noBreak&&p[o].length?p[o][p[o].length-1]+=e.value:p[o].push(e.value))}if(!p.text.length&&p.comment.length&&(p.text=p.comment,p.comment=[]),n)p.text=p.text.join(" "),r.push({name:p.text||i&&i.name,group:p.group.length?e(p.group.join(",")):[]});else{if(!p.address.length&&p.text.length){for(a=p.text.length-1;a>=0;a--)if(p.text[a].match(/^[^@\s]+@[^@\s]+$/)){p.address=p.text.splice(a,1);break}let e=function(e){return p.address.length?e:(p.address=[e.trim()]," ")};if(!p.address.length)for(a=p.text.length-1;a>=0&&(p.text[a]=p.text[a].replace(/\s*\b[^@\s]+@[^\s]+\b\s*/,e).trim(),!p.address.length);a--);}if(!p.text.length&&p.comment.length&&(p.text=p.comment,p.comment=[]),p.address.length>1&&(p.text=p.text.concat(p.address.splice(1))),p.text=p.text.join(" "),p.address=p.address.join(" "),!p.address&&n)return[];(i={address:p.address||p.text||"",name:p.text||p.address||""}).address===i.name&&((i.address||"").match(/@/)?i.name="":i.address=""),r.push(i)}return r}(t)).length&&(r=r.concat(t))}),a.flatten){let e=[],t=i=>{i.forEach(i=>{if(i.group)return t(i.group);e.push(i)})};return t(r),e}return r}},75745:(e,t,i)=>{"use strict";i.d(t,{A:()=>r});var a=i(56037),s=i.n(a);let n=process.env.MONGODB_URI;if(!n)throw Error("Please define the MONGODB_URI environment variable inside .env.local");let o=global.mongoose;o||(o=global.mongoose={conn:null,promise:null});let r=async function(){if(o.conn)return o.conn;o.promise||(o.promise=s().connect(n,{bufferCommands:!1}));try{o.conn=await o.promise}catch(e){throw o.promise=null,e}return o.conn}},77821:(e,t,i)=>{"use strict";let a=i(49074),s=i(91292);class n{constructor(e){e=e||{},this.options=e||{},this.name="StreamTransport",this.version=a.version,this.logger=s.getLogger(this.options,{component:this.options.component||"stream-transport"}),this.winbreak=["win","windows","dos","\r\n"].includes((e.newline||"").toString().toLowerCase())}send(e,t){e.message.keepBcc=!0;let i=e.data.envelope||e.message.getEnvelope(),a=e.message.messageId(),s=[].concat(i.to||[]);s.length>3&&s.push("...and "+s.splice(2).length+" more"),this.logger.info({tnx:"send",messageId:a},"Sending message %s to <%s> using %s line breaks",a,s.join(", "),this.winbreak?"<CR><LF>":"<LF>"),setImmediate(()=>{let i;try{i=e.message.createReadStream()}catch(e){return this.logger.error({err:e,tnx:"send",messageId:a},"Creating send stream failed for %s. %s",a,e.message),t(e)}if(!this.options.buffer)return i.once("error",e=>{this.logger.error({err:e,tnx:"send",messageId:a},"Failed creating message for %s. %s",a,e.message)}),t(null,{envelope:e.data.envelope||e.message.getEnvelope(),messageId:a,message:i});let s=[],n=0;i.on("readable",()=>{let e;for(;null!==(e=i.read());)s.push(e),n+=e.length}),i.once("error",e=>(this.logger.error({err:e,tnx:"send",messageId:a},"Failed creating message for %s. %s",a,e.message),t(e))),i.on("end",()=>t(null,{envelope:e.data.envelope||e.message.getEnvelope(),messageId:a,message:Buffer.concat(s,n)}))})}}e.exports=n},78335:()=>{},79524:(e,t,i)=>{"use strict";let a=i(79646).spawn,s=i(49074),n=i(91292);class o{constructor(e){e=e||{},this._spawn=a,this.options=e||{},this.name="Sendmail",this.version=s.version,this.path="sendmail",this.args=!1,this.winbreak=!1,this.logger=n.getLogger(this.options,{component:this.options.component||"sendmail"}),e&&("string"==typeof e?this.path=e:"object"==typeof e&&(e.path&&(this.path=e.path),Array.isArray(e.args)&&(this.args=e.args),this.winbreak=["win","windows","dos","\r\n"].includes((e.newline||"").toString().toLowerCase())))}send(e,t){let i,a,s;e.message.keepBcc=!0;let n=e.data.envelope||e.message.getEnvelope(),o=e.message.messageId();if([].concat(n.from||[]).concat(n.to||[]).some(e=>/^-/.test(e)))return t(Error("Can not send mail. Invalid envelope addresses."));i=this.args?["-i"].concat(this.args).concat(n.to):["-i"].concat(n.from?["-f",n.from]:[]).concat(n.to);let r=i=>{if(!s&&(s=!0,"function"==typeof t))if(i)return t(i);else return t(null,{envelope:e.data.envelope||e.message.getEnvelope(),messageId:o,response:"Messages queued for delivery"})};try{a=this._spawn(this.path,i)}catch(e){return this.logger.error({err:e,tnx:"spawn",messageId:o},"Error occurred while spawning sendmail. %s",e.message),r(e)}if(!a)return r(Error("sendmail was not found"));{a.on("error",e=>{this.logger.error({err:e,tnx:"spawn",messageId:o},"Error occurred when sending message %s. %s",o,e.message),r(e)}),a.once("exit",e=>{let t;if(!e)return r();t=127===e?Error("Sendmail command not found, process exited with code "+e):Error("Sendmail exited with code "+e),this.logger.error({err:t,tnx:"stdin",messageId:o},"Error sending message %s to sendmail. %s",o,t.message),r(t)}),a.once("close",r),a.stdin.on("error",e=>{this.logger.error({err:e,tnx:"stdin",messageId:o},"Error occurred when piping message %s to sendmail. %s",o,e.message),r(e)});let t=[].concat(n.to||[]);t.length>3&&t.push("...and "+t.splice(2).length+" more"),this.logger.info({tnx:"send",messageId:o},"Sending message %s to <%s>",o,t.join(", "));let i=e.message.createReadStream();i.once("error",e=>{this.logger.error({err:e,tnx:"stdin",messageId:o},"Error occurred when generating message %s. %s",o,e.message),a.kill("SIGINT"),r(e)}),i.pipe(a.stdin)}}}e.exports=o},79551:e=>{"use strict";e.exports=require("url")},79646:e=>{"use strict";e.exports=require("child_process")},80972:(e,t,i)=>{"use strict";i.d(t,{Q$:()=>n,y3:()=>o});let a={errors:{fetch_failed:"获取数据失败",network_error:"网络错误，请重试",validation_failed:"验证失败",unauthorized:"未授权访问",forbidden:"禁止访问",not_found:"资源未找到",internal_error:"服务器内部错误",invalid_request:"无效请求",missing_required_field:"缺少必需字段",duplicate_name:"名称已存在",create_failed:"创建失败",update_failed:"更新失败",delete_failed:"删除失败"},success:{created:"创建成功",updated:"更新成功",deleted:"删除成功",submitted:"提交成功",approved:"批准成功",rejected:"拒绝成功",published:"发布成功"},tools:{fetch_failed:"获取工具列表失败",create_failed:"创建工具失败",name_required:"name 是必需的",description_required:"description 是必需的",website_required:"website 是必需的",category_required:"category 是必需的",pricing_required:"pricing 是必需的",submitter_name_required:"submitterName 是必需的",submitter_email_required:"submitterEmail 是必需的",name_exists:"该工具名称已存在",submit_success:"工具提交成功，等待审核",approve_success:"工具审核通过",reject_success:"工具已拒绝",approve_failed:"审核通过失败",reject_failed:"拒绝失败",not_found:"工具未找到",update_success:"工具更新成功",update_failed:"工具更新失败",launch_date_already_set:"此工具已经选择了发布日期",free_date_restriction:"免费选项只能选择一个月后的日期",paid_date_restriction:"付费选项最早只能选择明天的日期",launch_date_set_success:"发布日期设置成功，工具已进入审核队列",edit_not_allowed:"当前状态不允许修改发布日期",already_published:"工具已发布，无法修改发布日期",launch_date_updated:"发布日期修改成功",publish_success:"工具发布成功",publish_failed:"工具发布失败"},user:{not_found:"用户未找到",unauthorized:"用户未授权",profile_update_success:"个人资料更新成功",profile_update_failed:"个人资料更新失败"},auth:{invalid_credentials:"无效的登录凭据",code_sent:"验证码已发送",code_send_failed:"验证码发送失败",invalid_code:"无效的验证码",login_success:"登录成功",login_failed:"登录失败",logout_success:"退出成功"},payment:{create_intent_failed:"创建支付意图失败",payment_success:"支付成功",payment_failed:"支付失败",webhook_error:"Webhook 处理错误",order_created:"订单创建成功，请完成支付",upgrade_order_created:"升级订单创建成功，请完成支付"},upload:{no_file:"请选择要上传的文件",invalid_type:"只支持 JPEG、PNG、GIF、WebP 格式的图片",file_too_large:"文件大小不能超过 5MB",upload_failed:"文件上传失败",upload_success:"文件上传成功"}},s={errors:{fetch_failed:"Failed to fetch data",network_error:"Network error, please try again",validation_failed:"Validation failed",unauthorized:"Unauthorized access",forbidden:"Access forbidden",not_found:"Resource not found",internal_error:"Internal server error",invalid_request:"Invalid request",missing_required_field:"Missing required field",duplicate_name:"Name already exists",create_failed:"Creation failed",update_failed:"Update failed",delete_failed:"Deletion failed"},success:{created:"Created successfully",updated:"Updated successfully",deleted:"Deleted successfully",submitted:"Submitted successfully",approved:"Approved successfully",rejected:"Rejected successfully",published:"Published successfully"},tools:{fetch_failed:"Failed to fetch tools list",create_failed:"Failed to create tool",name_required:"name is required",description_required:"description is required",website_required:"website is required",category_required:"category is required",pricing_required:"pricing is required",submitter_name_required:"submitterName is required",submitter_email_required:"submitterEmail is required",name_exists:"Tool name already exists",submit_success:"Tool submitted successfully, awaiting review",approve_success:"Tool approved successfully",reject_success:"Tool rejected successfully",approve_failed:"Failed to approve tool",reject_failed:"Failed to reject tool",not_found:"Tool not found",update_success:"Tool updated successfully",update_failed:"Failed to update tool",launch_date_already_set:"This tool has already selected a launch date",free_date_restriction:"Free option can only select dates one month later",paid_date_restriction:"Paid option can only select dates from tomorrow",launch_date_set_success:"Launch date set successfully, tool entered review queue",edit_not_allowed:"Current status does not allow modifying launch date",already_published:"Tool already published, cannot modify launch date",launch_date_updated:"Launch date updated successfully",publish_success:"Tool published successfully",publish_failed:"Failed to publish tool"},user:{not_found:"User not found",unauthorized:"User unauthorized",profile_update_success:"Profile updated successfully",profile_update_failed:"Failed to update profile"},auth:{invalid_credentials:"Invalid credentials",code_sent:"Verification code sent",code_send_failed:"Failed to send verification code",invalid_code:"Invalid verification code",login_success:"Login successful",login_failed:"Login failed",logout_success:"Logout successful"},payment:{create_intent_failed:"Failed to create payment intent",payment_success:"Payment successful",payment_failed:"Payment failed",webhook_error:"Webhook processing error",order_created:"Order created successfully, please complete payment",upgrade_order_created:"Upgrade order created successfully, please complete payment"},upload:{no_file:"Please select a file to upload",invalid_type:"Only JPEG, PNG, GIF, WebP image formats are supported",file_too_large:"File size cannot exceed 5MB",upload_failed:"File upload failed",upload_success:"File uploaded successfully"}};function n(e,t){let i=t.split("."),n="zh"===e?a:s;for(let t of i)if(!n||"object"!=typeof n||!(t in n))return"zh"===e?"操作失败":"Operation failed";else n=n[t];return"string"==typeof n?n:"zh"===e?"操作失败":"Operation failed"}function o(e){let t=e.headers.get("x-locale");if("en"===t||"zh"===t)return t;let i=e.headers.get("accept-language")||"",a=new URL(e.url).pathname;return a.startsWith("/en/")?"en":a.startsWith("/zh/")?"zh":i.includes("en")?"en":"zh"}},81630:e=>{"use strict";e.exports=require("http")},84681:(e,t,i)=>{"use strict";let a=i(94735),s=i(20068),n=i(16435),o=i(91292),r=i(21940),p=i(49074);class c extends a{constructor(e){let t;super(),"string"==typeof(e=e||{})&&(e={url:e});let i=e.service;"function"==typeof e.getSocket&&(this.getSocket=e.getSocket),e.url&&(t=o.parseConnectionUrl(e.url),i=i||t.service),this.options=o.assign(!1,e,t,i&&n(i)),this.logger=o.getLogger(this.options,{component:this.options.component||"smtp-transport"});let a=new s(this.options);this.name="SMTP",this.version=p.version+"[client:"+a.version+"]",this.options.auth&&(this.auth=this.getAuth({}))}getSocket(e,t){return setImmediate(()=>t(null,!1))}getAuth(e){if(!e)return this.auth;let t=!1,i={};if(this.options.auth&&"object"==typeof this.options.auth&&Object.keys(this.options.auth).forEach(e=>{t=!0,i[e]=this.options.auth[e]}),e&&"object"==typeof e&&Object.keys(e).forEach(a=>{t=!0,i[a]=e[a]}),!t)return!1;if("OAUTH2"!==(i.type||"").toString().toUpperCase())return{type:(i.type||"").toString().toUpperCase()||"LOGIN",user:i.user,credentials:{user:i.user||"",pass:i.pass,options:i.options},method:(i.method||"").trim().toUpperCase()||this.options.authMethod||!1};{if(!i.service&&!i.user)return!1;let e=new r(i,this.logger);return e.provisionCallback=this.mailer&&this.mailer.get("oauth2_provision_cb")||e.provisionCallback,e.on("token",e=>this.mailer.emit("token",e)),e.on("error",e=>this.emit("error",e)),{type:"OAUTH2",user:i.user,oauth2:e,method:"XOAUTH2"}}}send(e,t){this.getSocket(this.options,(i,a)=>{if(i)return t(i);let n=!1,r=this.options;a&&a.connection&&(this.logger.info({tnx:"proxy",remoteAddress:a.connection.remoteAddress,remotePort:a.connection.remotePort,destHost:r.host||"",destPort:r.port||"",action:"connected"},"Using proxied socket from %s:%s to %s:%s",a.connection.remoteAddress,a.connection.remotePort,r.host||"",r.port||""),r=o.assign(!1,r),Object.keys(a).forEach(e=>{r[e]=a[e]}));let p=new s(r);p.once("error",e=>{if(!n)return n=!0,p.close(),t(e)}),p.once("end",()=>{if(n)return;let e=setTimeout(()=>{if(n)return;n=!0;let e=Error("Unexpected socket close");p&&p._socket&&p._socket.upgrading&&(e.code="ETLS"),t(e)},1e3);try{e.unref()}catch(e){}});let c=()=>{let i=e.message.getEnvelope(),a=e.message.messageId(),s=[].concat(i.to||[]);s.length>3&&s.push("...and "+s.splice(2).length+" more"),e.data.dsn&&(i.dsn=e.data.dsn),this.logger.info({tnx:"send",messageId:a},"Sending message %s to <%s>",a,s.join(", ")),p.send(i,e.message.createReadStream(),(e,s)=>{if(n=!0,p.close(),e)return this.logger.error({err:e,tnx:"send"},"Send error for %s: %s",a,e.message),t(e);s.envelope={from:i.from,to:i.to},s.messageId=a;try{return t(null,s)}catch(e){this.logger.error({err:e,tnx:"callback"},"Callback error for %s: %s",a,e.message)}})};p.connect(()=>{if(n)return;let i=this.getAuth(e.data.auth);i&&(p.allowsAuth||r.forceAuth)?p.login(i,e=>{if(i&&i!==this.auth&&i.oauth2&&i.oauth2.removeAllListeners(),!n){if(e)return n=!0,p.close(),t(e);c()}}):c()})})}verify(e){let t;return e||(t=new Promise((t,i)=>{e=o.callbackPromise(t,i)})),this.getSocket(this.options,(t,i)=>{if(t)return e(t);let a=this.options;i&&i.connection&&(this.logger.info({tnx:"proxy",remoteAddress:i.connection.remoteAddress,remotePort:i.connection.remotePort,destHost:a.host||"",destPort:a.port||"",action:"connected"},"Using proxied socket from %s:%s to %s:%s",i.connection.remoteAddress,i.connection.remotePort,a.host||"",a.port||""),a=o.assign(!1,a),Object.keys(i).forEach(e=>{a[e]=i[e]}));let n=new s(a),r=!1;n.once("error",t=>{if(!r)return r=!0,n.close(),e(t)}),n.once("end",()=>{if(!r)return r=!0,e(Error("Connection closed"))});let p=()=>{if(!r)return r=!0,n.quit(),e(null,!0)};n.connect(()=>{if(r)return;let t=this.getAuth({});if(t&&(n.allowsAuth||a.forceAuth))n.login(t,t=>{if(!r){if(t)return r=!0,n.close(),e(t);p()}});else if(!t&&n.allowsAuth&&a.forceAuth){let t=Error("Authentication info was not provided");return t.code="NoAuth",r=!0,n.close(),e(t)}else p()})}),t}close(){this.auth&&this.auth.oauth2&&this.auth.oauth2.removeAllListeners(),this.emit("close")}}e.exports=c},89047:(e,t,i)=>{"use strict";let a=i(27910).Transform;class s extends a{constructor(){super(),this.lastByte=!1}_transform(e,t,i){e.length&&(this.lastByte=e[e.length-1]),this.push(e),i()}_flush(e){return 10===this.lastByte||(13===this.lastByte?this.push(Buffer.from("\n")):this.push(Buffer.from("\r\n"))),e()}}e.exports=s},89066:(e,t,i)=>{"use strict";let a=i(27910).Transform;function s(e){return"string"==typeof e&&(e=Buffer.from(e,"utf-8")),e.toString("base64")}function n(e,t){if(e=(e||"").toString(),t=t||76,e.length<=t)return e;let i=[],a=0,s=1024*t;for(;a<e.length;){let n=e.substr(a,s).replace(RegExp(".{"+t+"}","g"),"$&\r\n").trim();i.push(n),a+=s}return i.join("\r\n").trim()}class o extends a{constructor(e){super(),this.options=e||{},!1!==this.options.lineLength&&(this.options.lineLength=this.options.lineLength||76),this._curLine="",this._remainingBytes=!1,this.inputBytes=0,this.outputBytes=0}_transform(e,t,i){if("buffer"!==t&&(e=Buffer.from(e,t)),!e||!e.length)return setImmediate(i);this.inputBytes+=e.length,this._remainingBytes&&this._remainingBytes.length&&(e=Buffer.concat([this._remainingBytes,e],this._remainingBytes.length+e.length),this._remainingBytes=!1),e.length%3?(this._remainingBytes=e.slice(e.length-e.length%3),e=e.slice(0,e.length-e.length%3)):this._remainingBytes=!1;let a=this._curLine+s(e);if(this.options.lineLength){let e=(a=n(a,this.options.lineLength)).lastIndexOf("\n");e<0?(this._curLine=a,a=""):e===a.length-1?this._curLine="":(this._curLine=a.substr(e+1),a=a.substr(0,e+1))}a&&(this.outputBytes+=a.length,this.push(Buffer.from(a,"ascii"))),setImmediate(i)}_flush(e){this._remainingBytes&&this._remainingBytes.length&&(this._curLine+=s(this._remainingBytes)),this._curLine&&(this._curLine=n(this._curLine,this.options.lineLength),this.outputBytes+=this._curLine.length,this.push(this._curLine,"ascii"),this._curLine=""),e()}}e.exports={encode:s,wrap:n,Encoder:o}},89231:(e,t,i)=>{"use strict";let a=i(20068),s=i(91292).assign,n=i(21940),o=i(94735);class r extends o{constructor(e){if(super(),this.pool=e,this.options=e.options,this.logger=this.pool.logger,this.options.auth)switch((this.options.auth.type||"").toString().toUpperCase()){case"OAUTH2":{let e=new n(this.options.auth,this.logger);e.provisionCallback=this.pool.mailer&&this.pool.mailer.get("oauth2_provision_cb")||e.provisionCallback,this.auth={type:"OAUTH2",user:this.options.auth.user,oauth2:e,method:"XOAUTH2"},e.on("token",e=>this.pool.mailer.emit("token",e)),e.on("error",e=>this.emit("error",e));break}default:if(!this.options.auth.user&&!this.options.auth.pass)break;this.auth={type:(this.options.auth.type||"").toString().toUpperCase()||"LOGIN",user:this.options.auth.user,credentials:{user:this.options.auth.user||"",pass:this.options.auth.pass,options:this.options.auth.options},method:(this.options.auth.method||"").trim().toUpperCase()||this.options.authMethod||!1}}this._connection=!1,this._connected=!1,this.messages=0,this.available=!0}connect(e){this.pool.getSocket(this.options,(t,i)=>{if(t)return e(t);let n=!1,o=this.options;i&&i.connection&&(this.logger.info({tnx:"proxy",remoteAddress:i.connection.remoteAddress,remotePort:i.connection.remotePort,destHost:o.host||"",destPort:o.port||"",action:"connected"},"Using proxied socket from %s:%s to %s:%s",i.connection.remoteAddress,i.connection.remotePort,o.host||"",o.port||""),o=s(!1,o),Object.keys(i).forEach(e=>{o[e]=i[e]})),this.connection=new a(o),this.connection.once("error",t=>{if(this.emit("error",t),!n)return n=!0,e(t)}),this.connection.once("end",()=>{if(this.close(),n)return;n=!0;let t=setTimeout(()=>{if(n)return;let t=Error("Unexpected socket close");this.connection&&this.connection._socket&&this.connection._socket.upgrading&&(t.code="ETLS"),e(t)},1e3);try{t.unref()}catch(e){}}),this.connection.connect(()=>{if(!n)if(!this.auth||!this.connection.allowsAuth&&!o.forceAuth)return n=!0,this._connected=!0,e(null,!0);else this.connection.login(this.auth,t=>{if(!n){if(n=!0,t)return this.connection.close(),this.emit("error",t),e(t);this._connected=!0,e(null,!0)}})})})}send(e,t){if(!this._connected)return this.connect(i=>i?t(i):this.send(e,t));let i=e.message.getEnvelope(),a=e.message.messageId(),s=[].concat(i.to||[]);s.length>3&&s.push("...and "+s.splice(2).length+" more"),this.logger.info({tnx:"send",messageId:a,cid:this.id},"Sending message %s using #%s to <%s>",a,this.id,s.join(", ")),e.data.dsn&&(i.dsn=e.data.dsn),this.connection.send(i,e.message.createReadStream(),(e,s)=>{if(this.messages++,e)return this.connection.close(),this.emit("error",e),t(e);s.envelope={from:i.from,to:i.to},s.messageId=a,setImmediate(()=>{let e;this.messages>=this.options.maxMessages?((e=Error("Resource exhausted")).code="EMAXLIMIT",this.connection.close(),this.emit("error",e)):this.pool._checkRateLimit(()=>{this.available=!0,this.emit("available")})}),t(null,s)})}close(){this._connected=!1,this.auth&&this.auth.oauth2&&this.auth.oauth2.removeAllListeners(),this.connection&&this.connection.close(),this.emit("close")}}e.exports=r},89767:(e,t,i)=>{"use strict";let a=i(49074),s=i(91292);class n{constructor(e){e=e||{},this.options=e||{},this.name="JSONTransport",this.version=a.version,this.logger=s.getLogger(this.options,{component:this.options.component||"json-transport"})}send(e,t){e.message.keepBcc=!0;let i=e.data.envelope||e.message.getEnvelope(),a=e.message.messageId(),s=[].concat(i.to||[]);s.length>3&&s.push("...and "+s.splice(2).length+" more"),this.logger.info({tnx:"send",messageId:a},"Composing JSON structure of %s to <%s>",a,s.join(", ")),setImmediate(()=>{e.normalize((e,s)=>e?(this.logger.error({err:e,tnx:"send",messageId:a},"Failed building JSON structure for %s. %s",a,e.message),t(e)):(delete s.envelope,delete s.normalizedHeaders,t(null,{envelope:i,messageId:a,message:this.options.skipEncoding?s:JSON.stringify(s)})))})}}e.exports=n},91292:(e,t,i)=>{"use strict";let a,s=i(79551),n=i(28354),o=i(29021),r=i(38099),p=i(14985),c=i(91645),l=i(21820);try{a=l.networkInterfaces()}catch(e){}e.exports.networkInterfaces=a;let d=(t,i)=>{let a=e.exports.networkInterfaces;return!a||Object.keys(a).map(e=>a[e]).reduce((e,t)=>e.concat(t),[]).filter(e=>!e.internal||i).filter(e=>e.family==="IPv"+t||e.family===t).length>0},m=(e,t,i,a)=>{if(!d(e,(i=i||{}).allowInternalNetworkInterfaces))return a(null,[]);(p.Resolver?new p.Resolver(i):p)["resolve"+e](t,(e,t)=>{if(e){switch(e.code){case p.NODATA:case p.NOTFOUND:case p.NOTIMP:case p.SERVFAIL:case p.CONNREFUSED:case p.REFUSED:case"EAI_AGAIN":return a(null,[])}return a(e)}return a(null,Array.isArray(t)?t:[].concat(t||[]))})},h=e.exports.dnsCache=new Map,u=(e,t)=>e?Object.assign({servername:e.servername,host:e.addresses&&e.addresses.length?1===e.addresses.length?e.addresses[0]:e.addresses[Math.floor(Math.random()*e.addresses.length)]:null},t||{}):Object.assign({},t||{});function f(e,t){let i=!1,a=[],s=0;e.on("error",e=>{i||(i=!0,t(e))}),e.on("readable",()=>{let t;for(;null!==(t=e.read());)a.push(t),s+=t.length}),e.on("end",()=>{let e;if(!i){i=!0;try{e=Buffer.concat(a,s)}catch(e){return t(e)}t(null,e)}})}e.exports.resolveHostname=(e,t)=>{let i;return(!(e=e||{}).host&&e.servername&&(e.host=e.servername),!e.host||c.isIP(e.host))?t(null,u({addresses:[e.host],servername:e.servername||!1},{cached:!1})):h.has(e.host)&&(!(i=h.get(e.host)).expires||i.expires>=Date.now())?t(null,u(i.value,{cached:!0})):void m(4,e.host,e,(a,s)=>{if(a)return i?t(null,u(i.value,{cached:!0,error:a})):t(a);if(s&&s.length){let i={addresses:s,servername:e.servername||e.host};return h.set(e.host,{value:i,expires:Date.now()+(e.dnsTtl||3e5)}),t(null,u(i,{cached:!1}))}m(6,e.host,e,(a,s)=>{if(a)return i?t(null,u(i.value,{cached:!0,error:a})):t(a);if(s&&s.length){let i={addresses:s,servername:e.servername||e.host};return h.set(e.host,{value:i,expires:Date.now()+(e.dnsTtl||3e5)}),t(null,u(i,{cached:!1}))}try{p.lookup(e.host,{all:!0},(a,s)=>{if(a)return i?t(null,u(i.value,{cached:!0,error:a})):t(a);let n=!!s&&s.filter(e=>d(e.family)).map(e=>e.address).shift();if(s&&s.length&&!n&&console.warn(`Failed to resolve IPv${s[0].family} addresses with current network`),!n&&i)return t(null,u(i.value,{cached:!0}));let o={addresses:n?[n]:[e.host],servername:e.servername||e.host};return h.set(e.host,{value:o,expires:Date.now()+(e.dnsTtl||3e5)}),t(null,u(o,{cached:!1}))})}catch(e){if(i)return t(null,u(i.value,{cached:!0,error:e}));return t(e)}})})},e.exports.parseConnectionUrl=e=>{e=e||"";let t={};return[s.parse(e,!0)].forEach(e=>{let i;switch(e.protocol){case"smtp:":t.secure=!1;break;case"smtps:":t.secure=!0;break;case"direct:":t.direct=!0}!isNaN(e.port)&&Number(e.port)&&(t.port=Number(e.port)),e.hostname&&(t.host=e.hostname),e.auth&&(i=e.auth.split(":"),t.auth||(t.auth={}),t.auth.user=i.shift(),t.auth.pass=i.join(":")),Object.keys(e.query||{}).forEach(i=>{let a=t,s=i,n=e.query[i];switch(!isNaN(n)&&(n=Number(n)),n){case"true":n=!0;break;case"false":n=!1}if(0===i.indexOf("tls."))s=i.substr(4),t.tls||(t.tls={}),a=t.tls;else if(i.indexOf(".")>=0)return;s in a||(a[s]=n)})}),t},e.exports._logFunc=(e,t,i,a,s,...n)=>{let o={};Object.keys(i||{}).forEach(e=>{"level"!==e&&(o[e]=i[e])}),Object.keys(a||{}).forEach(e=>{"level"!==e&&(o[e]=a[e])}),e[t](o,s,...n)},e.exports.getLogger=(t,i)=>{let a={},s=["trace","debug","info","warn","error","fatal"];if(!(t=t||{}).logger)return s.forEach(e=>{a[e]=()=>!1}),a;let o=t.logger;return!0===t.logger&&(o=function(e){let t=0,i=new Map;e.forEach(e=>{e.length>t&&(t=e.length)}),e.forEach(e=>{let a=e.toUpperCase();a.length<t&&(a+=" ".repeat(t-a.length)),i.set(e,a)});let a=(e,t,a,...s)=>{let o="";t&&("server"===t.tnx?o="S: ":"client"===t.tnx&&(o="C: "),t.sid&&(o="["+t.sid+"] "+o),t.cid&&(o="[#"+t.cid+"] "+o)),(a=n.format(a,...s)).split(/\r?\n/).forEach(t=>{console.log("[%s] %s %s",new Date().toISOString().substr(0,19).replace(/T/," "),i.get(e),o+t)})},s={};return e.forEach(e=>{s[e]=a.bind(null,e)}),s}(s)),s.forEach(t=>{a[t]=(a,s,...n)=>{e.exports._logFunc(o,t,i,a,s,...n)}}),a},e.exports.callbackPromise=(e,t)=>function(){let i=Array.from(arguments),a=i.shift();a?t(a):e(...i)},e.exports.parseDataURI=e=>{let t,i=e.indexOf(",");if(!i)return e;let a=e.substring(i+1),s=e.substring(5,i).split(";"),n=s.length>1&&s[s.length-1];n&&0>n.indexOf("=")&&(t=n.toLowerCase(),s.pop());let o=s.shift()||"application/octet-stream",r={};for(let e of s){let t=e.indexOf("=");if(t>=0){let i=e.substring(0,t),a=e.substring(t+1);r[i]=a}}switch(t){case"base64":a=Buffer.from(a,"base64");break;case"utf8":a=Buffer.from(a);break;default:try{a=Buffer.from(decodeURIComponent(a))}catch(e){a=Buffer.from(a)}a=Buffer.from(a)}return{data:a,encoding:t,contentType:o,params:r}},e.exports.resolveContent=(t,i,a)=>{let s;a||(s=new Promise((t,i)=>{a=e.exports.callbackPromise(t,i)}));let n=t&&t[i]&&t[i].content||t[i],p=("object"==typeof t[i]&&t[i].encoding||"utf8").toString().toLowerCase().replace(/[-_\s]/g,"");if(!n)return a(null,n);if("object"==typeof n){if("function"==typeof n.pipe)return f(n,(e,s)=>{if(e)return a(e);t[i].content?t[i].content=s:t[i]=s,a(null,s)});else if(/^https?:\/\//i.test(n.path||n.href))return f(r(n.path||n.href),a);else if(/^data:/i.test(n.path||n.href)){let t=e.exports.parseDataURI(n.path||n.href);return t&&t.data?a(null,t.data):a(null,Buffer.from(0))}else if(n.path)return f(o.createReadStream(n.path),a)}return"string"!=typeof t[i].content||["utf8","usascii","ascii"].includes(p)||(n=Buffer.from(t[i].content,p)),setImmediate(()=>a(null,n)),s},e.exports.assign=function(){let e=Array.from(arguments),t=e.shift()||{};return e.forEach(e=>{Object.keys(e||{}).forEach(i=>{["tls","auth"].includes(i)&&e[i]&&"object"==typeof e[i]?(t[i]||(t[i]={}),Object.keys(e[i]).forEach(a=>{t[i][a]=e[i][a]})):t[i]=e[i]})}),t},e.exports.encodeXText=e=>{if(!/[^\x21-\x2A\x2C-\x3C\x3E-\x7E]/.test(e))return e;let t=Buffer.from(e),i="";for(let e=0,a=t.length;e<a;e++){let a=t[e];a<33||a>126||43===a||61===a?i+="+"+(a<16?"0":"")+a.toString(16).toUpperCase():i+=String.fromCharCode(a)}return i}},91570:(e,t,i)=>{"use strict";let a=i(34041),s=i(31534),n=i(74301),o=i(27910).PassThrough,r=i(29021),p=i(33873),c=i(55511);class l{constructor(e,t,i,a){this.options=e||{},this.keys=t,this.cacheTreshold=Number(this.options.cacheTreshold)||131072,this.hashAlgo=this.options.hashAlgo||"sha256",this.cacheDir=this.options.cacheDir||!1,this.chunks=[],this.chunklen=0,this.readPos=0,this.cachePath=!!this.cacheDir&&p.join(this.cacheDir,"message."+Date.now()+"-"+c.randomBytes(14).toString("hex")),this.cache=!1,this.headers=!1,this.bodyHash=!1,this.parser=!1,this.relaxedBody=!1,this.input=i,this.output=a,this.output.usingCache=!1,this.hasErrored=!1,this.input.on("error",e=>{this.hasErrored=!0,this.cleanup(),a.emit("error",e)})}cleanup(){this.cache&&this.cachePath&&r.unlink(this.cachePath,()=>!1)}createReadCache(){this.cache=r.createReadStream(this.cachePath),this.cache.once("error",e=>{this.cleanup(),this.output.emit("error",e)}),this.cache.once("close",()=>{this.cleanup()}),this.cache.pipe(this.output)}sendNextChunk(){if(this.hasErrored)return;if(this.readPos>=this.chunks.length)return this.cache?this.createReadCache():this.output.end();let e=this.chunks[this.readPos++];if(!1===this.output.write(e))return this.output.once("drain",()=>{this.sendNextChunk()});setImmediate(()=>this.sendNextChunk())}sendSignedOutput(){let e=0,t=()=>{if(e>=this.keys.length)return this.output.write(this.parser.rawHeaders),setImmediate(()=>this.sendNextChunk());let i=this.keys[e++],a=n(this.headers,this.hashAlgo,this.bodyHash,{domainName:i.domainName,keySelector:i.keySelector,privateKey:i.privateKey,headerFieldNames:this.options.headerFieldNames,skipFields:this.options.skipFields});return a&&this.output.write(Buffer.from(a+"\r\n")),setImmediate(t)};if(this.bodyHash&&this.headers)return t();this.output.write(this.parser.rawHeaders),this.sendNextChunk()}createWriteCache(){this.output.usingCache=!0,this.cache=r.createWriteStream(this.cachePath),this.cache.once("error",e=>{this.cleanup(),this.relaxedBody.unpipe(this.cache),this.relaxedBody.on("readable",()=>{for(;null!==this.relaxedBody.read(););}),this.hasErrored=!0,this.output.emit("error",e)}),this.cache.once("close",()=>{this.sendSignedOutput()}),this.relaxedBody.removeAllListeners("readable"),this.relaxedBody.pipe(this.cache)}signStream(){this.parser=new a,this.relaxedBody=new s({hashAlgo:this.hashAlgo}),this.parser.on("headers",e=>{this.headers=e}),this.relaxedBody.on("hash",e=>{this.bodyHash=e}),this.relaxedBody.on("readable",()=>{let e;if(!this.cache){for(;null!==(e=this.relaxedBody.read());)if(this.chunks.push(e),this.chunklen+=e.length,this.chunklen>=this.cacheTreshold&&this.cachePath)return this.createWriteCache()}}),this.relaxedBody.on("end",()=>{this.cache||this.sendSignedOutput()}),this.parser.pipe(this.relaxedBody),setImmediate(()=>this.input.pipe(this.parser))}}class d{constructor(e){this.options=e||{},this.keys=[].concat(this.options.keys||{domainName:e.domainName,keySelector:e.keySelector,privateKey:e.privateKey})}sign(e,t){let i=new o,a=e,s=!1;Buffer.isBuffer(e)?(s=e,a=new o):"string"==typeof e&&(s=Buffer.from(e),a=new o);let n=this.options;t&&Object.keys(t).length&&(n={},Object.keys(this.options||{}).forEach(e=>{n[e]=this.options[e]}),Object.keys(t||{}).forEach(e=>{e in n||(n[e]=t[e])}));let r=new l(n,this.keys,a,i);return setImmediate(()=>{r.signStream(),s&&setImmediate(()=>{a.end(s)})}),i}}e.exports=d},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")},96487:()=>{},97002:(e,t,i)=>{"use strict";let a=i(94735),s=i(49074),n=i(91292),o=i(39449);class r extends a{constructor(e){super(),e=e||{},this.options=e||{},this.ses=this.options.SES,this.name="SESTransport",this.version=s.version,this.logger=n.getLogger(this.options,{component:this.options.component||"ses-transport"}),this.maxConnections=Number(this.options.maxConnections)||1/0,this.connections=0,this.sendingRate=Number(this.options.sendingRate)||1/0,this.sendingRateTTL=null,this.rateInterval=1e3,this.rateMessages=[],this.pending=[],this.idling=!0,setImmediate(()=>{this.idling&&this.emit("idle")})}send(e,t){if(this.connections>=this.maxConnections||!this._checkSendingRate())return this.idling=!1,this.pending.push({mail:e,callback:t});this._send(e,(...e)=>{setImmediate(()=>t(...e)),this._sent()})}_checkRatedQueue(){if(this.connections>=this.maxConnections||!this._checkSendingRate())return;if(!this.pending.length){this.idling||(this.idling=!0,this.emit("idle"));return}let e=this.pending.shift();this._send(e.mail,(...t)=>{setImmediate(()=>e.callback(...t)),this._sent()})}_checkSendingRate(){clearTimeout(this.sendingRateTTL);let e=Date.now(),t=!1;for(let i=this.rateMessages.length-1;i>=0;i--)this.rateMessages[i].ts>=e-this.rateInterval&&(!t||this.rateMessages[i].ts<t)&&(t=this.rateMessages[i].ts),this.rateMessages[i].ts<e-this.rateInterval&&!this.rateMessages[i].pending&&this.rateMessages.splice(i,1);if(this.rateMessages.length<this.sendingRate)return!0;let i=Math.max(t+1001,e+20);this.sendingRateTTL=setTimeout(()=>this._checkRatedQueue(),e-i);try{this.sendingRateTTL.unref()}catch(e){}return!1}_sent(){this.connections--,this._checkRatedQueue()}isIdle(){return this.idling}_send(e,t){let i={ts:Date.now(),pending:!0};this.connections++,this.rateMessages.push(i);let a=e.data.envelope||e.message.getEnvelope(),s=e.message.messageId(),n=[].concat(a.to||[]);n.length>3&&n.push("...and "+n.splice(2).length+" more"),this.logger.info({tnx:"send",messageId:s},"Sending message %s to <%s>",s,n.join(", "));let r=t=>{e.data._dkim||(e.data._dkim={}),e.data._dkim.skipFields&&"string"==typeof e.data._dkim.skipFields?e.data._dkim.skipFields+=":date:message-id":e.data._dkim.skipFields="date:message-id";let i=e.message.createReadStream(),a=i.pipe(new o),s=[],n=0;a.on("readable",()=>{let e;for(;null!==(e=a.read());)s.push(e),n+=e.length}),i.once("error",e=>a.emit("error",e)),a.once("error",e=>{t(e)}),a.once("end",()=>t(null,Buffer.concat(s,n)))};setImmediate(()=>r((n,o)=>{var r;if(n)return this.logger.error({err:n,tnx:"send",messageId:s},"Failed creating message for %s. %s",s,n.message),i.pending=!1,t(n);let p={RawMessage:{Data:o},Source:a.from,Destinations:a.to};Object.keys(e.data.ses||{}).forEach(t=>{p[t]=e.data.ses[t]});let c=(this.ses.aws?this.ses.ses:this.ses)||{},l=this.ses.aws||{};r=(e,n)=>{let r;(e||!n)&&(n="us-east-1"),("function"==typeof c.send&&l.SendRawEmailCommand?c.send(new l.SendRawEmailCommand(p)):c.sendRawEmail(p).promise()).then(e=>{"us-east-1"===n&&(n="email"),i.pending=!1,t(null,{envelope:{from:a.from,to:a.to},messageId:"<"+e.MessageId+(/@/.test(e.MessageId)?"":"@"+n+".amazonses.com")+">",response:e.MessageId,raw:o})}).catch(e=>{this.logger.error({err:e,tnx:"send"},"Send error for %s: %s",s,e.message),i.pending=!1,t(e)})},c.config&&"function"==typeof c.config.region?c.config.region().then(e=>r(null,e)).catch(e=>r(e)):r(null,c.config&&c.config.region||"us-east-1")}))}verify(e){let t,i=(this.ses.aws?this.ses.ses:this.ses)||{},a=this.ses.aws||{},s={RawMessage:{Data:"From: invalid@invalid\r\nTo: invalid@invalid\r\n Subject: Invalid\r\n\r\nInvalid"},Source:"invalid@invalid",Destinations:["invalid@invalid"]};e||(t=new Promise((t,i)=>{e=n.callbackPromise(t,i)}));let o=t=>t&&"InvalidParameterValue"!==(t.code||t.Code)?e(t):e(null,!0);return"function"==typeof i.send&&a.SendRawEmailCommand?(s.RawMessage.Data=Buffer.from(s.RawMessage.Data),i.send(new a.SendRawEmailCommand(s),o)):i.sendRawEmail(s,o),t}}e.exports=r}};var t=require("../../../../webpack-runtime.js");t.C(e);var i=e=>t(t.s=e),a=t.X(0,[4243,580],()=>i(50623));module.exports=a})();